import Foundation
import SwiftData
import SwiftUI

/// 提供SwiftUI预览时使用的模拟数据
struct PreviewData {
    /// 模拟数据的ModelContainer - 使用AppSchema统一管理
    @MainActor
    static let container: ModelContainer = {
        do {
            let container = try EAAppSchema.createPreviewContainer()
            
            let context = container.mainContext
            
            // 创建示例用户
            let user = EAUser(username: "预览用户", email: "<EMAIL>")
            context.insert(user)
            // fetch一次，确保上下文一致
            let userId = user.id
            let userDescriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.id == userId })
            guard let contextUser = try? context.fetch(userDescriptor).first else {
                fatalError("预览数据：无法fetch用户")
            }
            
            // 创建用户设置
            let settings = EAUserSettings()
            settings.user = contextUser
            context.insert(settings)
            
            // 创建示例习惯
            let habit1 = EAHabit(name: "晨间阅读", iconName: "book.fill", targetFrequency: 1)
            context.insert(habit1)
            habit1.user = contextUser
            
            let habit2 = EAHabit(name: "健身锻炼", iconName: "figure.run", targetFrequency: 3)
            context.insert(habit2)
            habit2.user = contextUser
            
            // 创建完成记录
            let completion = EACompletion()
            context.insert(completion)
            completion.habit = habit1
            
            try context.save()
            return container
        } catch {
            fatalError("预览容器创建失败: \(error)")
        }
    }()
    
    /// 模拟数据的ModelContext
    @MainActor
    static var modelContext: ModelContext = {
        return ModelContext(container)
    }()
    
    /// 示例习惯 - 用于详情页预览
    static var sampleHabit: EAHabit = {
        let habit = EAHabit(
            name: "冥想练习",
            iconName: "brain.head.profile",
            targetFrequency: 7
        )
        habit.preferredTimeSlot = "morning"
        
        // 添加一些完成记录用于统计展示
        let calendar = Calendar.current
        let today = Date()
        
        // 创建最近30天的一些完成记录
        for i in 0..<15 {
            if let date = calendar.date(byAdding: .day, value: -i, to: today) {
                let completion = EACompletion()
                completion.habit = habit
                completion.date = date
            }
        }
        
        // 设置基本信息
        habit.name = "晨间冥想"
        habit.habitDescription = "每天早晨进行10分钟的冥想练习"
        habit.iconName = "brain.head.profile"
        habit.category = "健康"
        habit.targetFrequency = 1
        habit.frequencyType = "daily"
        habit.reminderEnabled = true
        habit.color = "#14B8A6"
        
        return habit
    }()
    
    /// 示例用户资料
    static var sampleUserProfile: EAUser = {
        let user = EAUser(username: "预览用户", email: "<EMAIL>")
        user.isPro = false
        return user
    }()
        
    /// 示例习惯列表
    static var sampleHabits: [EAHabit] {
        let habit1 = EAHabit(name: "晨间阅读", iconName: "book.fill", targetFrequency: 1)
        let habit2 = EAHabit(name: "健身锻炼", iconName: "figure.run", targetFrequency: 3)
        let habit3 = EAHabit(name: "冥想练习", iconName: "leaf.fill", targetFrequency: 7)
        
        // 为习惯添加完成记录
        for habit in [habit1, habit2, habit3] {
            let completion = EACompletion()
            completion.habit = habit
        }
        
        return [habit1, habit2, habit3]
    }
    
    /// 示例用户
    static var sampleUser: EAUser = {
        let user = EAUser(username: "叶同学", email: "<EMAIL>")
        return user
    }()
    
    /// 初始化预览数据
    @MainActor
    static func initializePreviewData() {
        let context = modelContext
        
        // 检查是否已有数据，避免重复创建
        let existingHabits = try? context.fetch(FetchDescriptor<EAHabit>())
        if existingHabits?.isEmpty == false {
            return
        }
        
        // 创建示例用户
        let user = sampleUser
        context.insert(user)
        
        // 创建示例习惯
        let habit = EAHabit(name: "晨间阅读", iconName: "book.fill", targetFrequency: 1)
        context.insert(habit)
        
        // 创建示例完成记录 - 使用新的初始化方法建立正确关系
        let completion = EACompletion()
        context.insert(completion)
        completion.habit = habit
        
        // 保存数据
        try? context.save()
    }
    
    static var sampleUserSettings: EAUserSettings = {
        let settings = EAUserSettings()
        settings.preferredCoachStyle = "温柔鼓励型"
        settings.notificationsEnabled = true
        settings.preferredReminderTimes = ["09:00", "18:00"]
        return settings
    }()
    
    static var sampleContent: [EAContent] {
        return [
            EAContent(title: "计划的力量", content: "小计划，大改变", contentType: "motivation", isPro: false),
            EAContent(title: "专注冥想", content: "5分钟专注练习", contentType: "psychology_exercise", isPro: true),
            EAContent(title: "成功故事", content: "用户分享的成功经验", contentType: "success_story", isPro: false)
        ]
    }
    
    static var sampleAIMessages: [EAAIMessage] {
        return [
            EAAIMessage(userMessage: "今天感觉有点累", aiResponse: "理解你的感受，要不要试试轻松一点的活动？", conversationType: "daily_checkin"),
            EAAIMessage(userMessage: "我想养成阅读计划", aiResponse: "很棒的想法！我们可以从每天10分钟开始", conversationType: "habit_creation")
        ]
    }
    
    @MainActor
    static func createPreviewContainer() -> ModelContainer? {
        do {
            let container = try EAAppSchema.createPreviewContainer()
            let context = container.mainContext
            
            // 创建示例用户
            let user = EAUser(username: "预览用户", email: "<EMAIL>")
            context.insert(user)
            // fetch一次，确保上下文一致
            let userId = user.id
            let userDescriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.id == userId })
            guard let contextUser = try? context.fetch(userDescriptor).first else {
                fatalError("预览数据：无法fetch用户")
            }
            
            // 创建用户设置
            let settings = EAUserSettings()
            settings.user = contextUser
            context.insert(settings)
            
            // 创建示例习惯
            let habit1 = EAHabit(name: "晨间阅读", iconName: "book.fill", targetFrequency: 1)
            context.insert(habit1)
            habit1.user = contextUser
            
            let habit2 = EAHabit(name: "健身锻炼", iconName: "figure.run", targetFrequency: 3)
            context.insert(habit2)
            habit2.user = contextUser
            
            // 创建完成记录
            let completion = EACompletion()
            context.insert(completion)
            completion.habit = habit1
            
            try context.save()
            return container
        } catch {
            fatalError("预览容器创建失败: \(error)")
        }
    }
    
    // MARK: - 社区功能示例数据
    
    /// 示例社区用户
    static var sampleCommunityUsers: [EAUser] {
        let users = [
            ("华", "<EMAIL>", true),
            ("小明", "<EMAIL>", false),
            ("晓雨", "<EMAIL>", false),
            ("志强", "<EMAIL>", true)
        ]
        
        return users.map { (username, email, isPro) in
            let user = EAUser(username: username, email: email)
            user.isPro = isPro
            user.creationDate = Calendar.current.date(byAdding: .day, value: -Int.random(in: 1...90), to: Date()) ?? Date()
            
            // 为用户创建社交档案
            let socialProfile = EAUserSocialProfile()
            socialProfile.user = user
            user.socialProfile = socialProfile
            
            return user
        }
    }
    
    // ✅ MVP精简：移除社区功能相关的预览数据
    // 专注于核心功能的预览支持
}

// MARK: - 中文输入测试视图
struct ChineseInputTestView: View {
    @State private var testText = ""
    
    var body: some View {
        VStack {
            TextField("请输入中文测试", text: $testText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding()
            
            Text("输入内容：\(testText)")
                .padding()
            
            Button("清空") {
                testText = ""
            }
            .padding()
        }
        .navigationTitle("中文输入测试")
    }
}

#Preview("中文输入测试") {
    NavigationView {
        ChineseInputTestView()
    }
}