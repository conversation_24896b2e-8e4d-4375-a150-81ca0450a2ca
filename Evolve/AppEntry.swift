import SwiftUI
import SwiftData
import UserNotifications

@main
struct EvolveApp: App {
    
    // 🔑 新架构：使用统一的AppState管理所有状态
    @StateObject private var appState = EAAppState()
    
    // ✅ 修复：使用AppSchema统一管理所有模型，确保"唯一真相源"
    @MainActor
    static let modelContainer: ModelContainer = {
        do {
            return try EAAppSchema.createProductionContainer()
        } catch {
            #if DEBUG
            fatalError("Failed to create ModelContainer: \(error)")
            #else
            fatalError("Database initialization failed. Please restart the app.")
            #endif
        }
    }()
    
    init() {
        #if DEBUG
        print("🚀 [AppEntry] 开始简化的应用初始化")
        #endif
        
        // 🔑 新架构：简化初始化，核心服务将在onAppear中延迟创建
        // 这避免了复杂的依赖链初始化，防止启动时的竞态条件
        
        requestNotificationPermission()
        
        #if DEBUG
        print("✅ [AppEntry] 简化初始化完成，核心服务将按需创建")
        #endif
    }
    
    var body: some Scene {
        WindowGroup {
            // 🔑 关键：使用sessionID强制视图树重建
            Group {
                if let sessionManager = appState.sessionManager,
                   let repositoryContainer = appState.repositoryContainer {
                    // 数据栈已就绪，显示应用内容
                    RootView()
                        .environment(\.repositoryContainer, repositoryContainer)
                        .environmentObject(sessionManager)
                        .environment(\.aiInAppReminderManager, appState.aiInAppReminderManager)
                        .environment(\.repositoryPerformanceMonitor, appState.repositoryPerformanceMonitor)
                        .environment(\.sheetManager, appState.sheetManager)
                        .environment(\.notificationDelegate, appState.notificationDelegate)
                        .environment(\.imageCacheService, appState.imageCacheService)

                        .userIdentityServices(
                            guard: appState.userIntegrityGuard ?? EAUserIntegrityGuard(repositoryContainer: repositoryContainer),
                            initializer: appState.asyncProfileInitializer ?? EAAsyncProfileInitializer(repositoryContainer: repositoryContainer),
                            monitor: appState.userIdentityMonitor ?? EAUserIdentityMonitor(repositoryContainer: repositoryContainer)
                        )
                        .errorHandlingServices(
                            userFriendlyService: appState.userFriendlyErrorService ?? EAUserFriendlyErrorService(),
                            degradationManager: appState.gracefulDegradationManager ?? EAGracefulDegradationManager(userFriendlyErrorService: appState.userFriendlyErrorService ?? EAUserFriendlyErrorService())
                        )
                        .performanceThresholdManager(appState.performanceThresholdManager ?? EAPerformanceThresholdManager(performanceMonitor: appState.repositoryPerformanceMonitor))
                        .withSheetManager(appState.sheetManager)
                        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("EASessionLogoutCompleted"))) { _ in
                            // 🔑 关键：监听登出事件，重建数据栈
                            appState.rebuildDataStackForUserSwitch()
                        }
                } else {
                    // 数据栈初始化中，显示加载界面
                    LoadingView()
                }
            }
            .id(appState.sessionID) // 🔑 关键：使用sessionID强制整个视图树重建
            .onAppear {
                // 🔑 应用启动时创建数据栈
                if !appState.isDataStackReady {
                    appState.createCoreDataStack()
                }
                
                // 🔑 启动会话恢复
                Task {
                    await appState.sessionManager?.restoreSessionOnAppLaunch()
                }
                
                // 🔑 配置性能监控
                Task {
                    await appState.configurePerformanceMonitoring()
                }
            }
        }
        .modelContainer(Self.modelContainer)
    }
    
    /// 请求通知权限
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                #if DEBUG
                // 调试环境下处理通知权限结果
                _ = (granted, error)
                #endif
            }
        }
    }
}

// MARK: - 加载视图

struct LoadingView: View {
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            VStack(spacing: 20) {
                ProgressView()
                    .scaleEffect(1.5)
                    .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                
                Text("初始化数据栈...")
                    .foregroundColor(.white)
                    .font(.system(size: 16, weight: .medium))
            }
        }
    }
}

// MARK: - RootView（简化的根视图状态管理）

struct RootView: View {
    @EnvironmentObject private var sessionManager: EASessionManager
    @Environment(\.repositoryContainer) private var repositoryContainer
    @StateObject private var onboardingViewModel = EAOnboardingViewModel()
    @State private var authViewModel: EAAuthViewModel?
    
    var body: some View {
        Group {
            if !onboardingViewModel.hasCompletedOnboarding {
                // 新用户：显示引导页面
                OnboardingFlow()
                    .environmentObject(onboardingViewModel)
            } else if sessionManager.isLoggedIn {
                // ✅ 已登录：显示主界面
                MainAppView()
                    .environmentObject(sessionManager)
                    .environmentObject(authViewModel ?? EAAuthViewModel())
            } else {
                // ✅ 未登录：显示认证界面
                AuthenticationFlow()
                    .environmentObject(authViewModel ?? EAAuthViewModel())
                    .environmentObject(sessionManager)
            }
        }
        .onAppear {
            setupAuthViewModel()
        }
        .preferredColorScheme(.dark)
    }
    
    /// 设置认证视图模型
    private func setupAuthViewModel() {
        if authViewModel == nil {
            let viewModel = EAAuthViewModel(authService: nil, sessionManager: sessionManager)
            if let container = repositoryContainer {
                viewModel.setRepositoryContainer(container)
            }
            self.authViewModel = viewModel
        }
    }
}

// MARK: - 引导流程视图

struct OnboardingFlow: View {
    @EnvironmentObject var onboardingViewModel: EAOnboardingViewModel
    
    var body: some View {
        EAOnboardingView()
            .environmentObject(onboardingViewModel)
    }
}

// MARK: - 认证流程视图

struct AuthenticationFlow: View {
    @EnvironmentObject var authViewModel: EAAuthViewModel
    @EnvironmentObject var sessionManager: EASessionManager
    @State private var currentView: AuthView = .login
    
    enum AuthView {
        case login
        case registration
        case forgotPassword
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                EABackgroundView(style: .authentication, showParticles: true)
                
                // 认证视图
                switch currentView {
                case .login:
                    EALoginView()
                        .environmentObject(authViewModel)
                case .registration:
                    EARegistrationView()
                        .environmentObject(authViewModel)
                case .forgotPassword:
                    EAForgotPasswordView()
                        .environmentObject(authViewModel)
                }
            }
            .navigationBarHidden(true)
        }
    }
}

// MARK: - 主应用视图

struct MainAppView: View {
    @EnvironmentObject var sessionManager: EASessionManager
    @EnvironmentObject var authViewModel: EAAuthViewModel
    @Environment(\.repositoryContainer) private var repositoryContainer
    
    var body: some View {
        EAMainTabView()
            .environmentObject(sessionManager)
            .environmentObject(authViewModel)

    }
}

// MARK: - 预览

#Preview("App - 新用户（引导页面）") {
    @Previewable @State var onboardingViewModel: EAOnboardingViewModel = {
        let vm = EAOnboardingViewModel()
        vm.hasCompletedOnboarding = false
        return vm
    }()
    
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: PreviewData.container)
    let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
    
    RootView()
        .modelContainer(PreviewData.container)
        .environment(\.repositoryContainer, repositoryContainer)
        .environmentObject(sessionManager)
        .environmentObject(onboardingViewModel)
}

#Preview("App - 老用户未登录（认证页面）") {
    @Previewable @State var onboardingViewModel: EAOnboardingViewModel = {
        let vm = EAOnboardingViewModel()
        vm.hasCompletedOnboarding = true
        return vm
    }()
    
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: PreviewData.container)
    let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
    
    RootView()
        .modelContainer(PreviewData.container)
        .environment(\.repositoryContainer, repositoryContainer)
        .environmentObject(sessionManager)
        .environmentObject(onboardingViewModel)
}

#Preview("App - 已登录（主应用）") {
    @Previewable @State var onboardingViewModel: EAOnboardingViewModel = {
        let vm = EAOnboardingViewModel()
        vm.hasCompletedOnboarding = true
        return vm
    }()
    
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: PreviewData.container)
    let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
    
    RootView()
        .modelContainer(PreviewData.container)
        .environment(\.repositoryContainer, repositoryContainer)
        .environmentObject(sessionManager)
        .environmentObject(onboardingViewModel)
        .task {
            // 模拟登录状态用于预览
            #if DEBUG
            await sessionManager.simulateLogin()
            #endif
            PreviewData.initializePreviewData()
        }
}