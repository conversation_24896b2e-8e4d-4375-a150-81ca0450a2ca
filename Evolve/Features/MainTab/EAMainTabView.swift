import SwiftUI
import SwiftData

/// Tab栏高度PreferenceKey - 用于测量自定义Tab栏的精确高度
/// 实现自上而下的安全区域传递架构，废除手动padding补偿模式
/// 符合EA命名规范，确保项目代码一致性
/// 🔑 优化：添加防抖机制，减少频繁触发
struct EATabBarHeightPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0

    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        let newValue = nextValue()
        // 🔑 优化：只在高度变化超过0.5pt时才更新，减少微小变化的触发
        if abs(value - newValue) > 0.5 {
            value = newValue
        }
    }
}

/// 主Tab导航视图
/// 完全按照原型图设计，包含四个核心功能模块：今日、图鉴、灵境、我的
/// 使用自定义设计实现毛玻璃背景和发光效果，支持设备旋转和动态布局适配
/// 🔑 修复：实现懒加载机制，避免启动时所有Tab页面同时初始化
struct EAMainTabView: View {
    @State private var selectedTab: Tab = .today
    @State private var customTabBarHeight: CGFloat = 0  // 存储自定义Tab栏的精确高度
    @Environment(\.repositoryContainer) private var repositoryContainer
    @EnvironmentObject var sessionManager: EASessionManager
    
    // 🔑 新增：懒加载状态管理 - 跟踪哪些Tab已经被初始化
    @State private var initializedTabs: Set<Tab> = [.today] // 默认初始化今日页面
    
    /// Tab枚举定义（MVP简化版本）
    enum Tab: String, CaseIterable {
        case today = "today"
        case atlas = "atlas"
        case auraSpace = "auraSpace"
        case me = "me"

        var title: String {
            switch self {
            case .today: return "今日"
            case .atlas: return "图鉴"
            case .auraSpace: return "灵境"
            case .me: return "我的"
            }
        }
        
        var iconSelected: String {
            switch self {
            case .today: return "sun.max.fill"
            case .atlas: return "book.fill"
            case .auraSpace: return "brain.head.profile"
            case .me: return "person.fill"
            }
        }

        var iconUnselected: String {
            switch self {
            case .today: return "sun.max"
            case .atlas: return "book"
            case .auraSpace: return "brain.head.profile"
            case .me: return "person"
            }
        }
        
        var subtitle: String {
            switch self {
            case .today: return "查看今日计划进度和能量状态"
            case .atlas: return "管理你的计划生态系统"
            case .auraSpace: return "与AI智慧核心深度交流"
            case .me: return "个人中心与设置"
            }
        }
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .bottom) {
                // 🔑 修复：主内容区域 - 实现真正的懒加载
                Group {
                    // 🔑 关键修复：只渲染当前选中的Tab页面
                    currentTabContent
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .ignoresSafeArea(.all) // 让内容完全占据屏幕
                .padding(.bottom, customTabBarHeight) // 为自定义Tab栏预留精确空间
                
                // 自定义Tab Bar - 使用GeometryReader测量精确高度
                VStack(spacing: 0) {
                    // Tab Bar内容区域 - 使用GeometryReader测量高度并广播
                    GeometryReader { tabGeometry in
                        CustomTabBarContent(
                            selectedTab: $selectedTab,
                            screenWidth: geometry.size.width
                        )
                        .frame(height: 49)
                        .frame(maxWidth: .infinity)
                        .preference(key: EATabBarHeightPreferenceKey.self, value: tabGeometry.size.height)
                    }
                    .frame(height: 49) // 固定Tab Bar内容高度

                    // 底部安全区域占位 - 简化处理
                    Color.clear
                        .frame(height: 0)
                        .safeAreaInset(edge: .bottom) {
                            Color.clear.frame(height: 0)
                        }
                }
                .background(
                    // 毛玻璃背景效果 - 延伸到屏幕底部
                    ZStack {
                        // 深色半透明背景
                        Color.black.opacity(0.2)
                        
                        // 毛玻璃效果
                        Rectangle()
                            .fill(.ultraThinMaterial)
                            .environment(\.colorScheme, .dark) // 强制深色毛玻璃
                    }
                    .ignoresSafeArea(.all, edges: .bottom)
                )
                .overlay(
                    // 顶部边框线
                    Rectangle()
                        .fill(Color.white.opacity(0.1))
                        .frame(height: 1),
                    alignment: .top
                )
            }
        }
        .ignoresSafeArea(.all) // 确保GeometryReader获取完整屏幕尺寸
        .onPreferenceChange(EATabBarHeightPreferenceKey.self) { height in
            // 🔑 优化：防抖处理，避免频繁更新
            if abs(customTabBarHeight - height) > 0.5 {
                customTabBarHeight = height
            }
        }
        // 🔑 新增：监听Tab切换，实现懒加载
        .onChange(of: selectedTab) { _, newTab in
            // ✅ 修复：避免在视图更新周期中发布状态变化
            // 使用Task将状态更新推迟到下一个运行循环
            Task { @MainActor in
                // 当用户切换到新Tab时，将其标记为已初始化
                if !initializedTabs.contains(newTab) {
                    _ = withAnimation(.easeInOut(duration: 0.2)) {
                        initializedTabs.insert(newTab)
                    }
                }
            }
        }
    }
    
    // 🔑 新增：当前Tab内容视图 - 实现真正的懒加载
    @ViewBuilder
    private var currentTabContent: some View {
        switch selectedTab {
        case .today:
            EATodayView(sessionManager: sessionManager)
                .environmentObject(sessionManager)
        case .atlas:
            if initializedTabs.contains(.atlas) {
                EAAtlasView()
            } else {
                LazyTabPlaceholder(tabName: "图鉴")
            }
        case .auraSpace:
            if initializedTabs.contains(.auraSpace) {
                EAAuraSpaceView()
            } else {
                LazyTabPlaceholder(tabName: "灵境")
            }

        case .me:
            if initializedTabs.contains(.me) {
                NavigationStack {
                    if let repositoryContainer = repositoryContainer {
                        EAMeView(viewModel: EAMeViewModel(sessionManager: sessionManager), repositoryContainer: repositoryContainer)
                            .environmentObject(sessionManager)
                    } else {
                        Text("加载中...")
                            .foregroundColor(.white)
                    }
                }
            } else {
                LazyTabPlaceholder(tabName: "我的")
            }
        }
    }
}

// 🔑 新增：懒加载占位视图
struct LazyTabPlaceholder: View {
    let tabName: String
    
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.white)
            
            Text("正在加载\(tabName)...")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.opacity(0.9))
    }
}

/// 自定义Tab Bar内容组件 - 支持动态宽度适配和设备旋转
struct CustomTabBarContent: View {
    @Binding var selectedTab: EAMainTabView.Tab
    let screenWidth: CGFloat
    
    // 计算Tab项目的最佳宽度 - 修复双重padding问题
    private var tabItemWidth: CGFloat {
        let totalTabs = EAMainTabView.Tab.allCases.count
        let edgePadding: CGFloat = 12 // 左右各6pt边距，保持视觉美观
        let availableWidth = screenWidth - edgePadding
        return availableWidth / CGFloat(totalTabs)
    }
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(EAMainTabView.Tab.allCases, id: \.self) { tab in
                TabBarItem(
                    tab: tab,
                    isSelected: selectedTab == tab,
                    itemWidth: tabItemWidth
                ) {
                    // 点击切换Tab，添加触觉反馈和流畅动画
                    // ✅ 修复：withAnimation返回Void，不需要使用_忽略
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7, blendDuration: 0)) {
                        selectedTab = tab
                    }
                    
                    // 增强触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.prepare() // 预准备，提升响应速度
                    impactFeedback.impactOccurred()
                }
            }
        }
        .frame(maxWidth: .infinity) // 确保HStack占据最大可用宽度
        .padding(.horizontal, 6) // 统一的水平边距，避免重复计算
    }
}

/// Tab Bar单个项目组件 - 支持动态宽度和增强的视觉效果
struct TabBarItem: View {
    let tab: EAMainTabView.Tab
    let isSelected: Bool
    let itemWidth: CGFloat
    let action: () -> Void
    
    @State private var isPressed: Bool = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 2) { // iOS标准图标文字间距2pt
                Spacer(minLength: 0) // 顶部弹性间距
                
                // 图标容器 - 增加按压反馈
                ZStack {
                    // 按压时的背景圆圈
                    if isPressed {
                        Circle()
                            .fill(Color.white.opacity(0.1))
                            .frame(width: 32, height: 32)
                            .transition(.scale.combined(with: .opacity))
                    }
                    
                    // 图标
                    Image(systemName: isSelected ? tab.iconSelected : tab.iconUnselected)
                        .font(.system(size: 25, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(isSelected ? Color(red: 0.25, green: 0.88, blue: 0.82) : Color(red: 0.54, green: 0.54, blue: 0.56))
                        .shadow(
                            color: isSelected ? Color(red: 0.25, green: 0.88, blue: 0.82).opacity(0.6) : .clear,
                            radius: isSelected ? 6 : 0,
                            x: 0,
                            y: 0
                        )
                        .scaleEffect(isSelected ? 1.05 : (isPressed ? 0.95 : 1.0))
                }
                
                // 文字标签
                Text(tab.title)
                    .font(.system(size: 10, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? Color(red: 0.25, green: 0.88, blue: 0.82) : Color(red: 0.54, green: 0.54, blue: 0.56))
                    .shadow(
                        color: isSelected ? Color(red: 0.25, green: 0.88, blue: 0.82).opacity(0.4) : .clear,
                        radius: isSelected ? 3 : 0,
                        x: 0,
                        y: 0
                    )
                    .lineLimit(1)
                    .minimumScaleFactor(0.8) // 确保在较窄屏幕上文字不会被截断
                
                Spacer(minLength: 0) // 底部弹性间距
            }
            .frame(width: itemWidth, height: 49) // 使用精确的分配宽度，移除最小宽度限制
            .contentShape(Rectangle()) // 扩大点击区域
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0) // 整体按压效果
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            // ✅ 修复：withAnimation返回Void，不需要使用_忽略
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .animation(.spring(response: 0.3, dampingFraction: 0.7, blendDuration: 0), value: isSelected)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .accessibilityLabel(tab.title)
        .accessibilityHint(tab.subtitle)
        .accessibilityAddTraits(isSelected ? [.isSelected] : [])
    }
}

#Preview("主Tab导航") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
        .onAppear {
            PreviewData.initializePreviewData()
        }
}

#Preview("主Tab导航 - 浅色模式") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.light)
}

#Preview("主Tab导航 - 横屏模式") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
        .onAppear {
            PreviewData.initializePreviewData()
        }
}

#Preview("主Tab导航 - iPhone SE") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
        .onAppear {
            PreviewData.initializePreviewData()
        }
}

#Preview("主Tab导航 - 大屏设备") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
        .onAppear {
            PreviewData.initializePreviewData()
        }
} 