import SwiftUI
import SwiftData
import UserNotifications
import os.log

/// 计划创建页面的ViewModel
/// 负责管理计划创建流程中的所有状态和业务逻辑
@MainActor
class EAHabitCreationViewModel: ObservableObject {
    // MARK: - 🔍 调试：ViewModel生命周期追踪
    private let viewModelID = UUID() // 用于识别实例的唯一ID

    // MARK: - Properties
    private var sessionManager: EASessionManager
    private var repositoryContainer: EARepositoryContainer?
    let notificationService: EANotificationService
    


    // ✅ 新增：日志记录器
    private let logger = Logger(subsystem: "com.evolve.habitcreation", category: "EAHabitCreationViewModel")

    // ✅ iOS 18.5修复：Task管理，防止内存泄漏
    private var currentTasks: Set<Task<Void, Never>> = []
    
    // MARK: - Published Properties
    @Published var habitName: String = ""
    @Published var selectedIcon: String = "star.fill"
    
    // 🔑 优化：新的频率设置 - 移除默认选择，让用户自己选择
    @Published var selectedFrequencyType: FrequencyType = .weekly
    @Published var selectedWeekdays: Set<Int> = [] // 🔑 优化：不预设默认选择，让用户自己选择
    @Published var dailyTarget: Int = 0 // 🔑 优化：不预设默认选择，让用户自己选择
    @Published var monthlyTarget: Int = 0 // 🔑 优化：不预设默认选择，让用户自己选择
    @Published var monthlyMode: MonthlyMode = .target // 每月执行模式
    @Published var selectedMonthlyDates: Set<Int> = [] // 🔑 优化：不预设默认选择，让用户自己选择
    
    // ✅ 新增：一次性计划日期属性
    @Published var selectedOneTimeDates: [String] = []

    // ✅ iOS 18.5性能优化：日历数据预计算属性
    @Published var calendarDays: [CalendarDayCreation] = []
    @Published var currentMonth: Date = Date()
    @Published var isCalendarLoading: Bool = false

    // 向后兼容的频率设置
    @Published var targetFrequency: Int = 3
    
    @Published var selectedTimeSlot: String = "morning"
    @Published var selectedCategory: String = "健康"
    @Published var selectedDifficulty: String = "简单"
    @Published var habitTags: [String] = []
    @Published var aiCoachingStyle: String = "温柔鼓励型"
    
    // 提醒时间设置
    @Published var reminderTimes: [Date] = []
    
    // AI相关属性
    @Published var showAISuggestion: Bool = false
    @Published var aiSuggestion: String = ""
    @Published var aiSuggested: Bool = false
    
    // 错误处理
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    
    // UI状态
    @Published var isLoading: Bool = false
    @Published var showingAlert: Bool = false
    @Published var alertMessage: String = ""
    @Published var currentStep: Int = 1
    
    // 编辑模式
    var editingHabit: EAHabit?
    
    // 计算属性：是否为编辑模式
    var isEditMode: Bool {
        return editingHabit != nil
    }
    
    // 🔑 新增：计算属性 - 是否可以创建习惯
    var canCreateHabit: Bool {
        return !habitName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // 常量
    let totalSteps = 4

    // ✅ iOS 18.5性能优化：静态Calendar和DateFormatter实例，避免重复创建
    private static let calendar = Calendar.current
    private static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return formatter
    }()

    // 使用静态实例的便捷访问器
    private var calendar: Calendar { Self.calendar }
    private var dateFormatter: DateFormatter { Self.dateFormatter }

    // MARK: - ✅ iOS 18.5性能优化：日历数据预计算方法

    /// 异步生成日历数据，避免阻塞主线程
    /// ✅ iPad性能优化：避免Task.detached，使用更安全的异步方式
    func generateCalendarDays(for month: Date) async -> [CalendarDayCreation] {
        #if DEBUG
        print("🔧 [日历生成] 开始生成日历数据，目标月份: \(month)")
        #endif

        // ✅ iPad优化：直接在当前Task中执行，避免Context切换
        let calendar = Self.calendar
        let today = Date()
        var days: [CalendarDayCreation] = []

        // ✅ 防御性编程：安全获取当前月份的第一天
        guard let monthInterval = calendar.dateInterval(of: .month, for: month) else {
            #if DEBUG
            print("🔧 [日历生成] 错误：无法获取月份区间")
            #endif
            return []
        }
        let startOfMonth = monthInterval.start

        // ✅ 防御性编程：安全获取这个月第一天是周几
        let firstWeekday = calendar.component(.weekday, from: startOfMonth)
        let adjustedFirstWeekday = firstWeekday == 1 ? 7 : firstWeekday - 1  // 调整为1=周一

        // ✅ 防御性编程：安全获取这个月有多少天
        guard let daysRange = calendar.range(of: .day, in: .month, for: month) else {
            #if DEBUG
            print("🔧 [日历生成] 错误：无法获取月份天数范围")
            #endif
            return []
        }
        let daysInMonth = daysRange.count

        #if DEBUG
        print("🔧 [日历生成] 月份信息 - 开始日期: \(startOfMonth), 天数: \(daysInMonth)")
        #endif

        // ✅ 关键修复：重新添加前置空白日期填充，保证日历对齐
        //            并在后面补足空白，使总单元格保持 7×6 = 42，避免月度切换时高度跳变
        if adjustedFirstWeekday > 1 {
            for i in stride(from: adjustedFirstWeekday - 2, through: 0, by: -1) {
                // 使用月份开始日期向前偏移，确保每个占位日期唯一
                if let blankDate = calendar.date(byAdding: .day, value: -(i + 1), to: startOfMonth) {
                    days.append(
                        CalendarDayCreation(
                            date: blankDate,
                            dayNumber: "",
                            isSelectable: false,
                            isSelected: false,
                            isToday: false,
                            isInCurrentMonth: false,
                            isWeekend: false
                        )
                    )
                }
            }
        }

        // ✅ iPad优化：获取当前选中的日期，避免重复的MainActor调用
        let currentSelectedDates = await MainActor.run {
            return Set(self.selectedOneTimeDates)
        }

        // 添加这个月的所有实际日期
        for day in 1...daysInMonth {
            guard let date = calendar.date(byAdding: .day, value: day - 1, to: startOfMonth) else {
                continue
            }

            let dayNumber = "\(day)"
            let isToday = calendar.isDate(date, inSameDayAs: today)
            let isInCurrentMonth = true
            let weekday = calendar.component(.weekday, from: date)
            let isWeekend = weekday == 1 || weekday == 7  // 周日=1, 周六=7

            // 检查是否已被选中 - 使用预获取的数据
            let dateString = Self.dateFormatter.string(from: date)
            let isSelected = currentSelectedDates.contains(dateString)

            let calendarDay = CalendarDayCreation(
                date: date,
                dayNumber: dayNumber,
                isSelectable: true,
                isSelected: isSelected,
                isToday: isToday,
                isInCurrentMonth: isInCurrentMonth,
                isWeekend: isWeekend
            )

            days.append(calendarDay)
        }

        // ✅ 关键修复：后置空白填充，确保总格子数为42（6行×7列），防止不同月份网格高度变化导致布局抖动
        let remaining = 42 - days.count
        if remaining > 0 {
            for i in 0..<remaining {
                if let blankDate = calendar.date(byAdding: .day, value: daysInMonth + i + 1, to: startOfMonth) {
                    days.append(
                        CalendarDayCreation(
                            date: blankDate,
                            dayNumber: "",
                            isSelectable: false,
                            isSelected: false,
                            isToday: false,
                            isInCurrentMonth: false,
                            isWeekend: false
                        )
                    )
                }
            }
        }

        #if DEBUG
        print("🔧 [日历生成] 完成，生成了 \(days.count) 个日期")
        #endif

        return days
    }

    /// 更新当前月份并重新生成日历数据
    /// ✅ 月份切换优化：提升响应性，减少用户等待时间
    func updateCurrentMonth(_ newMonth: Date) async {
        await MainActor.run {
            // ✅ 防抖处理：使用更精确的月份比较，避免时区问题
            let calendar = Calendar.current
            let newMonthComponents = calendar.dateComponents([.year, .month], from: newMonth)
            let currentMonthComponents = calendar.dateComponents([.year, .month], from: self.currentMonth)

            // 如果是同一个月，直接返回
            if newMonthComponents.year == currentMonthComponents.year &&
               newMonthComponents.month == currentMonthComponents.month {
                #if DEBUG
                print("🔧 [月份切换] 相同月份，跳过更新")
                #endif
                return
            }

            #if DEBUG
            print("🔧 [月份切换] 从 \(currentMonthComponents.year ?? 0)-\(currentMonthComponents.month ?? 0) 切换到 \(newMonthComponents.year ?? 0)-\(newMonthComponents.month ?? 0)")
            #endif

            // ✅ 关键修复：立即更新月份和加载状态，确保UI立即响应
            self.currentMonth = newMonth
            self.isCalendarLoading = true
        }

        // ✅ 性能优化：异步生成日历数据，不阻塞UI
        let newCalendarDays = await generateCalendarDays(for: newMonth)

        await MainActor.run {
            // ✅ 关键修复：直接替换数据，避免清空导致的UI闪烁
            self.calendarDays = newCalendarDays
            self.isCalendarLoading = false

            #if DEBUG
            print("🔧 [月份切换] 完成，新日历数据数量: \(newCalendarDays.count)")
            #endif
        }
    }

    /// 切换日期选择状态
    /// ✅ iPad性能优化：避免重新生成整个日历，只更新必要的状态
    func toggleDateSelection(_ date: Date) async {
        let dateString = dateFormatter.string(from: date)

        await MainActor.run {
            if self.selectedOneTimeDates.contains(dateString) {
                self.selectedOneTimeDates.removeAll { $0 == dateString }
            } else {
                self.selectedOneTimeDates.append(dateString)
            }

            // ✅ iPad优化：直接更新现有日历数据，避免重新生成
            self.updateCalendarDaysSelection()
        }
    }

    /// ✅ iPad性能优化：直接更新现有日历数据的选择状态，避免重新生成
    @MainActor
    private func updateCalendarDaysSelection() {
        let selectedDatesSet = Set(selectedOneTimeDates)

        calendarDays = calendarDays.map { day in
            let dayDateString = dateFormatter.string(from: day.date)
            let isSelected = selectedDatesSet.contains(dayDateString)

            return CalendarDayCreation(
                date: day.date,
                dayNumber: day.dayNumber,
                isSelectable: day.isSelectable,
                isSelected: isSelected,
                isToday: day.isToday,
                isInCurrentMonth: day.isInCurrentMonth,
                isWeekend: day.isWeekend
            )
        }
    }
    
    // MARK: - 初始化
    
    /// ✅ iOS 18.5真机修复：轻量级初始化方法，避免主线程阻塞
    /// 初始化方法（强制依赖注入）
    init(sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer? = nil, editingHabit: EAHabit? = nil) {
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
        self.editingHabit = editingHabit
        self.notificationService = EANotificationService(sessionManager: sessionManager)

        // ✅ MVP精简：移除星际能量服务，专注于核心习惯创建功能

        // ✅ iOS 18.5真机修复：延迟加载编辑数据，避免init中的复杂操作
        // 编辑数据将在onAppear中加载，避免在init中进行复杂的数据处理

        // 🔍 调试：ViewModel生命周期追踪
        #if DEBUG
        print("✅ VIEWMODEL INIT: 实例 \(viewModelID) 已创建")
        #endif
    }



    // ✅ iOS 18.5真机修复：移除needsReinit复杂状态检查，改用声明式状态管理

    /// ✅ iOS 18.5修复：清理资源，防止内存泄漏
    deinit {
        // 🔍 调试：ViewModel生命周期追踪
        #if DEBUG
        print("❌ VIEWMODEL DEINIT: 实例 \(viewModelID) 已销毁")
        #endif

        // 取消所有正在进行的任务
        for task in currentTasks {
            task.cancel()
        }
        currentTasks.removeAll()
    }

    
    // MARK: - 公共方法
    
    /// ✅ iOS 18.5真机修复：设置Repository容器，延迟初始化重型服务
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container

        // ✅ 关键修复：不在这里创建重型服务，避免主线程阻塞
        // 星际能量服务将在实际需要时异步创建

        // ✅ 关键修复：移除自动初始化日历数据，避免与频率切换产生竞态条件
        // 日历数据将在用户选择一次性频率时按需初始化

        #if DEBUG
        print("🚀 [ViewModel初始化] EAHabitCreationViewModel 已创建")
        #endif
    }



    /// ✅ 关键修复：确保日历数据已初始化（用于频率切换时调用）
    @MainActor
    func ensureCalendarDataInitialized() async {
        #if DEBUG
        print("🔧 [日历初始化] 开始初始化日历数据，当前月份: \(currentMonth)，当前加载状态: \(isCalendarLoading)")
        #endif

        // ✅ 关键修复：强制重新初始化，确保频率切换时数据正确
        // 移除防重复逻辑，因为频率切换时需要强制刷新
        isCalendarLoading = true

        // ✅ 强制重新生成日历数据，确保数据完整性
        let newCalendarDays = await generateCalendarDays(for: currentMonth)

        // 🔥 决定性修复：强制主线程发布，确保UI更新通知可靠传递
        await MainActor.run {
            self.calendarDays = newCalendarDays
            self.isCalendarLoading = false

            #if DEBUG
            print("🔧 [日历初始化] 完成，生成日历数据数量: \(newCalendarDays.count)")
            #endif
        }
    }
    
    /// ✅ iOS 18.5真机修复：更新SessionManager，延迟初始化复杂依赖
    func updateSessionManager(_ newSessionManager: EASessionManager) {
        self.sessionManager = newSessionManager

        // ✅ iOS 18.5真机修复：简化初始化逻辑，移除复杂状态检查
        // 使用Repository模式，无需手动管理服务初始化
    }

    // ✅ iOS 18.5真机修复：移除星际能量服务初始化，改用Repository模式

    /// 生成AI建议
    func generateAISuggestion() {
        // ✅ iOS 18.5修复：使用Task管理避免内存泄漏
        let task = Task { [weak self] in
            guard let self = self else { return }

            // ✅ iOS 18.5修复：确保UI更新在主线程
            await MainActor.run {
                self.isLoading = true
            }

            // 这里应该调用AI服务生成建议
            // 暂时使用模拟数据
            let suggestion = await self.generateMockAISuggestion()

            // ✅ iOS 18.5修复：确保UI更新在主线程
            await MainActor.run {
                self.aiSuggestion = suggestion
                self.showAISuggestion = true
                self.isLoading = false
            }
        }

        // 将任务添加到管理集合
        currentTasks.insert(task)

        // 任务完成后清理
        Task { [weak self] in
            await task.value
            await MainActor.run {
                _ = self?.currentTasks.remove(task)
            }
        }
    }
    
    /// 创建新习惯（iOS 18.5真机性能优化版本）
    ///
    /// 使用Repository的安全创建方法，确保所有SwiftData操作在同一Context中进行
    /// 添加超时保护和性能监控，确保用户体验
    func createHabit() async {
        let startTime = CFAbsoluteTimeGetCurrent() // 性能监控开始
        // ✅ iOS 18.5真机修复：简化依赖检查，避免复杂逻辑导致崩溃
        // ✅ 修复：使用布尔测试替代未使用的变量
        guard repositoryContainer != nil else {
            await MainActor.run {
                errorMessage = "系统初始化未完成，请稍后重试"
                showError = true
                isLoading = false
            }
            return
        }

        // ✅ 增强错误处理：获取当前用户ID
        guard let userId = await getCurrentUserId() else {
            await MainActor.run {
                errorMessage = "无法获取用户信息，请重新登录"
                showError = true
                isLoading = false
            }
            return
        }

        // ✅ 增强错误处理：验证表单数据
        guard let validatedFrequency = validateFormData() else {
            await MainActor.run {
                isLoading = false
            }
            return
        }

        // ✅ 改进用户反馈：设置加载状态
        await MainActor.run {
            isLoading = true
            errorMessage = ""
            showError = false
        }
        
        do {
            // ✅ iOS 18.5真机修复：添加超时保护机制
            try await withTimeout(seconds: 10.0) { [weak self] in
                guard let self = self else { return }

                // ✅ 增强错误处理：检查Repository容器
                guard let container = self.repositoryContainer else {
                    await MainActor.run {
                        self.errorMessage = "系统初始化错误，请重启应用后重试"
                        self.showError = true
                        self.isLoading = false
                    }
                    throw NSError(domain: "EAHabitCreationViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "Repository容器未初始化"])
                }

                // 准备提醒时间数据
                let reminderTimeStrings: [String]
                if !self.reminderTimes.isEmpty {
                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "HH:mm"
                    reminderTimeStrings = self.reminderTimes.map { dateFormatter.string(from: $0) }
                } else {
                    reminderTimeStrings = []
                }

                // ✅ iOS 18.5性能优化：简化创建逻辑，优先使用快速创建模式
                let newHabit: EAHabit
                if self.selectedFrequencyType == .oneTime && self.selectedOneTimeDates.count > 10 {
                    // 简化创建模式：先创建基础计划，再异步更新复杂属性
                    newHabit = try await container.habitRepository.createHabit(
                        name: self.habitName,
                        iconName: self.selectedIcon,
                        targetFrequency: validatedFrequency,
                        frequencyType: self.selectedFrequencyType.rawValue,
                        category: self.selectedCategory,
                        difficulty: self.selectedDifficulty,
                        for: userId
                    )

                    // 异步更新复杂属性，避免阻塞主创建流程
                    let capturedDates = self.selectedOneTimeDates
                    let capturedTimeSlot = self.localizedTimeSlot(self.selectedTimeSlot)
                    Task.detached {
                        do {
                            newHabit.selectedOneTimeDates = capturedDates
                            newHabit.reminderTimes = reminderTimeStrings
                            newHabit.reminderEnabled = !reminderTimeStrings.isEmpty
                            newHabit.preferredTimeSlot = capturedTimeSlot
                            try await container.habitRepository.saveHabit(newHabit)
                        } catch {
                            // 复杂属性更新失败，但基础计划已创建
                            // 静默处理，避免影响用户体验
                        }
                    }
                } else {
                    // 标准创建模式
                    newHabit = try await container.habitRepository.createHabitSafely(
                        name: self.habitName,
                        iconName: self.selectedIcon,
                        targetFrequency: validatedFrequency,
                        frequencyType: self.selectedFrequencyType.rawValue,
                        selectedWeekdays: Array(self.selectedWeekdays),
                        dailyTarget: self.dailyTarget,
                        monthlyTarget: self.monthlyTarget,
                        monthlyMode: self.monthlyMode.rawValue,
                        selectedMonthlyDates: Array(self.selectedMonthlyDates),
                        selectedOneTimeDates: self.selectedOneTimeDates,
                        category: self.selectedCategory,
                        difficulty: self.selectedDifficulty,
                        reminderTimes: reminderTimeStrings,
                        reminderEnabled: !self.reminderTimes.isEmpty,
                        preferredTimeSlot: self.localizedTimeSlot(self.selectedTimeSlot),
                        for: userId
                    )
                }

                // 发送通知
                self.notifyHabitCreated(habitId: newHabit.id)

                // ✅ iOS 18.5真机性能优化：将星际能量奖励完全异步化，避免阻塞主创建流程
                // 使用后台队列执行，不等待结果，确保创建流程快速完成
                let currentHabitName = self.habitName
                let currentDifficulty = self.selectedDifficulty

                // 🚨 关键修复：使用Task而非Task.detached，避免SwiftData Context冲突
                // Task.detached会在不同的Context中执行，可能导致SwiftData崩溃
                Task { [weak self] in
                    guard let self = self else { return }
                    // ✅ 修复：移除不必要的await，repositoryContainer是同步属性
                    guard let container = self.repositoryContainer else { return }

                // ✅ MVP精简：移除星际能量奖励系统
                // 专注于核心习惯创建功能
            }

            // ✅ iOS 18.5性能监控：记录创建耗时
            let endTime = CFAbsoluteTimeGetCurrent()
            let executionTime = endTime - startTime

            // 如果创建时间过长，记录性能警告
            if executionTime > 3.0 {
                logger.warning("习惯创建耗时过长: \(String(format: "%.2f", executionTime))秒")
            }

                // ✅ iOS 18.2修复：成功创建后重置状态
                await MainActor.run {
                    self.isLoading = false
                    // 重置表单状态，避免状态残留
                    self.resetFormState()
                }
            } // 超时保护结束
        } catch {
            await MainActor.run {
                // 检查是否为超时错误
                if error is TimeoutError {
                    errorMessage = "创建习惯超时，请检查网络连接后重试"
                    showError = true
                    isLoading = false
                } else {
                    handleCreateHabitError(error)
                }
            }
        }
    }
    
    /// 更新习惯
    func updateHabit() async {
        guard let habit = editingHabit else {
            await MainActor.run {
                showError = true
                errorMessage = "无法找到要编辑的习惯"
            }
            return
        }

        // ✅ iOS 18.5修复：确保UI更新在主线程
        await MainActor.run {
            isLoading = true
            showError = false
        }
        
        // 更新习惯属性（确保关系完整性）
        habit.name = habitName
        habit.iconName = selectedIcon
        habit.frequencyType = selectedFrequencyType.rawValue
        habit.targetFrequency = selectedFrequencyType == .weekly ? selectedWeekdays.count : 
                               (selectedFrequencyType == .daily ? dailyTarget : monthlyTarget)
        habit.selectedWeekdays = Array(selectedWeekdays)
        habit.dailyTarget = dailyTarget
        habit.monthlyTarget = monthlyTarget
        habit.monthlyMode = monthlyMode.rawValue
        habit.selectedMonthlyDates = Array(selectedMonthlyDates)
        habit.category = selectedCategory
        habit.difficulty = selectedDifficulty
        
        // 🔑 新增：保存提醒时间设置
        if !reminderTimes.isEmpty {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "HH:mm"
            
            habit.reminderTimes = reminderTimes.map { date in
                dateFormatter.string(from: date)
            }
            habit.reminderEnabled = true
        } else {
            habit.reminderTimes = []
            habit.reminderEnabled = false
        }
        
        // 🔑 新增：保存时间段偏好（本地化处理）
        habit.preferredTimeSlot = localizedTimeSlot(selectedTimeSlot)
        
        // 保存到数据库
        do {
            guard let container = repositoryContainer else {
                await MainActor.run {
                    showError = true
                    errorMessage = "Repository容器未初始化"
                    isLoading = false
                }
                return
            }
            try await container.habitRepository.saveHabit(habit)
        } catch {
            await MainActor.run {
                showError = true

                // 提供用户友好的错误信息
                let errorDescription = error.localizedDescription.lowercased()
                if errorDescription.contains("incompatible") &&
                   errorDescription.contains("nsmanagedobjectmodel") {
                    errorMessage = "数据模型需要更新，请重启应用后重试"
                } else {
                    errorMessage = "更新习惯失败，请重试"
                }

                isLoading = false
            }
            return
        }

        // ✅ iOS 18.5修复：确保UI更新和通知在主线程
        await MainActor.run {
            // 更新成功
            isLoading = false
            // 发送习惯编辑通知
            NotificationCenter.default.post(name: NSNotification.Name("HabitEdited"), object: habit)
            // 发送习惯数据变化通知
            NotificationCenter.default.post(name: NSNotification.Name("HabitDataChanged"), object: nil)
            // 通知父视图关闭
            NotificationCenter.default.post(name: NSNotification.Name("HabitUpdated"), object: habit)
        }
    }
    
    /// 采纳AI建议
    func adoptAISuggestion(_ suggestion: String) {
        habitName = suggestion
        aiSuggested = true
        showAISuggestion = false
    }
    
    /// 重置表单（公共方法，供外部调用）
    func resetFormData() {
        resetForm()
    }
    
    /// 重置表单状态（iOS 18.2修复：防止状态残留）
    private func resetFormState() {
        errorMessage = ""
        showError = false
        aiSuggested = false
        showAISuggestion = false
    }
    
    // MARK: - 私有方法
    
    /// ✅ iOS 18.5真机修复：异步加载习惯数据，避免主线程阻塞
    func loadHabitData(_ habit: EAHabit) {
        // ✅ 防止重复加载导致的无限循环
        guard editingHabit?.id != habit.id else {
            return
        }

        // 设置编辑模式
        editingHabit = habit

        // ✅ iOS 18.5真机修复：简化数据验证，避免复杂逻辑
        // 基本信息加载（快速操作）
        habitName = habit.name.isEmpty ? "新习惯" : habit.name
        selectedIcon = habit.iconName.isEmpty ? "star.fill" : habit.iconName
        selectedCategory = habit.category.isEmpty ? "健康" : habit.category
        selectedDifficulty = habit.difficulty.isEmpty ? "简单" : habit.difficulty

        // ✅ 异步加载复杂数据，避免阻塞主线程
        Task { @MainActor in
            await loadComplexHabitData(habit)
        }
    }

    /// ✅ iOS 18.5真机修复：异步加载复杂习惯数据
    private func loadComplexHabitData(_ habit: EAHabit) async {
        // ✅ iOS 18.5真机修复：加载提醒时间设置（防御性编程）
        if !habit.reminderTimes.isEmpty {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "HH:mm"
            dateFormatter.locale = Locale(identifier: "en_US_POSIX")

            reminderTimes = habit.reminderTimes.compactMap { timeString in
                guard !timeString.isEmpty && timeString.count >= 4 else { return nil }
                return dateFormatter.date(from: timeString)
            }
        }

        // 加载时间段偏好
        if let timeSlot = habit.preferredTimeSlot, !timeSlot.isEmpty {
            selectedTimeSlot = timeSlot
        }

        // ✅ iOS 18.5真机修复：简化频率类型加载
        selectedFrequencyType = FrequencyType(rawValue: habit.frequencyType) ?? .daily

        // 基本频率数据加载（简化版本）
        selectedWeekdays = habit.selectedWeekdays.isEmpty ? [1, 3, 5] : Set(habit.selectedWeekdays)
        dailyTarget = max(1, habit.dailyTarget)
        monthlyTarget = max(1, habit.monthlyTarget)
        monthlyMode = MonthlyMode(rawValue: habit.monthlyMode) ?? .target
        selectedMonthlyDates = habit.selectedMonthlyDates.isEmpty ? [1, 15] : Set(habit.selectedMonthlyDates.filter { $0 >= 1 && $0 <= 31 })
        selectedOneTimeDates = habit.selectedOneTimeDates

        // 简化的频率计算
        targetFrequency = max(1, habit.targetFrequency > 0 ? habit.targetFrequency : 1)
    }
    
    private func generateMockAISuggestion() async -> String {
        // 模拟AI建议生成
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        
        let suggestions = [
            "建议从每周3次开始，逐步增加频率。选择固定的时间点有助于养成习惯。",
            "可以考虑将习惯与现有的日常活动结合，比如刷牙后或睡前。",
            "设置小而具体的目标，比如每次10分钟，比设置过大的目标更容易坚持。"
        ]
        
        // ✅ 防御性编程：安全访问数组元素
        return suggestions.randomElement() ?? (suggestions.isEmpty ? "建议从简单的目标开始，逐步建立习惯。" : suggestions[0])
    }
    
    private func resetForm() {
        habitName = ""
        selectedIcon = "⭐"
        selectedFrequencyType = .weekly
        selectedWeekdays = [] // 🔑 优化：重置时不设置默认选择
        dailyTarget = 0 // 🔑 优化：重置时不设置默认选择
        monthlyTarget = 0 // 🔑 优化：重置时不设置默认选择
        monthlyMode = .target
        selectedMonthlyDates = [] // 🔑 优化：重置时不设置默认选择
        selectedOneTimeDates = []  // ✅ 新增：重置一次性计划日期
        targetFrequency = 3
        selectedCategory = "健康"
        selectedDifficulty = "简单"
        habitTags = []
        aiCoachingStyle = "温柔鼓励型"

        // 🔥 决定性修复：重置日历相关状态，防止状态残留
        isCalendarLoading = false
        calendarDays = []
        currentMonth = Date()

        aiSuggested = false
    }
    
    // MARK: - 辅助方法
    private func showError(_ message: String) {
        errorMessage = message
        showError = true
    }
    
    /// 🔑 新增：本地化时间段显示
    private func localizedTimeSlot(_ timeSlot: String) -> String {
        switch timeSlot.lowercased() {
        case "morning", "早晨":
            return "早晨"
        case "afternoon", "下午":
            return "下午"
        case "evening", "晚上":
            return "晚上"
        case "night", "深夜":
            return "深夜"
        default:
            return timeSlot // 如果已经是中文或其他值，直接返回
        }
    }
    
    func nextStep() {
        if currentStep < totalSteps {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentStep += 1
            }
        }
    }
    
    func previousStep() {
        if currentStep > 1 {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentStep -= 1
            }
        }
    }
    
    func addTag(_ tag: String) {
        let trimmedTag = tag.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedTag.isEmpty && !habitTags.contains(trimmedTag) {
            habitTags.append(trimmedTag)
        }
    }
    
    func removeTag(_ tag: String) {
        habitTags.removeAll { $0 == tag }
    }
    
    // MARK: - 私有辅助方法
    
    /// 获取当前用户ID（iOS 18.2安全版本）
    /// - Returns: 当前用户ID，失败时返回nil并设置错误信息
    private func getCurrentUserId() async -> UUID? {
        // 🔑 重构：使用新的安全异步接口
        guard let user = await sessionManager.safeCurrentUser else {
            await MainActor.run {
                errorMessage = "请先登录后再创建习惯"
                showError = true
            }
            return nil
        }
        return user.id
    }
    
    /// 获取当前用户（保留用于其他用途）
    /// - Returns: 当前用户对象，失败时返回nil并设置错误信息
    private func getCurrentUser() async -> EAUser? {
        // 🔑 重构：使用新的安全异步接口
        guard let user = await sessionManager.safeCurrentUser else {
            errorMessage = "请先登录后再创建习惯"
            return nil
        }
        return user
    }
    
    /// 验证表单数据
    /// - Returns: 验证后的目标频率，验证失败时返回nil并设置错误信息
    private func validateFormData() -> Int? {
        let validatedTargetFrequency: Int
        
        switch selectedFrequencyType {
        case .weekly:
            if selectedWeekdays.isEmpty {
                errorMessage = "请选择至少一天执行习惯"
                showError = true
                return nil
            }
            validatedTargetFrequency = selectedWeekdays.count
            
        case .daily:
            if dailyTarget <= 0 {
                errorMessage = "请选择每日目标次数"
                showError = true
                return nil
            }
            validatedTargetFrequency = dailyTarget
            
        case .monthly:
            if monthlyMode == .target {
                if monthlyTarget <= 0 {
                    errorMessage = "请选择每月目标次数"
                    showError = true
                    return nil
                }
                validatedTargetFrequency = monthlyTarget
            } else {
                if selectedMonthlyDates.isEmpty {
                    errorMessage = "请选择至少一个执行日期"
                    showError = true
                    return nil
                }
                validatedTargetFrequency = selectedMonthlyDates.count
            }
        case .oneTime:
            // ✅ 增强验证：检查一次性计划日期
            if selectedOneTimeDates.isEmpty {
                errorMessage = "请选择至少一个执行日期"
                showError = true
                return nil
            }

            // ✅ 新增：验证日期数量限制
            if selectedOneTimeDates.count > 20 {
                errorMessage = "最多只能选择20个日期"
                showError = true
                return nil
            }

            // ✅ 新增：验证日期格式和有效性
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            dateFormatter.locale = Locale(identifier: "en_US_POSIX")

            var invalidDates: [String] = []

            for dateString in selectedOneTimeDates {
                // 验证日期格式
                guard dateString.count == 10,
                      let _ = dateFormatter.date(from: dateString) else {
                    invalidDates.append(dateString)
                    continue
                }

                // ✅ 可选：验证日期不能是过去的日期（根据需求决定是否启用）
                // if date < Calendar.current.startOfDay(for: currentDate) {
                //     invalidDates.append(dateString)
                // }
            }

            if !invalidDates.isEmpty {
                errorMessage = "选择的日期格式无效，请重新选择"
                showError = true
                return nil
            }

            // ✅ 新增：验证日期去重（防止重复日期）
            let uniqueDates = Set(selectedOneTimeDates)
            if uniqueDates.count != selectedOneTimeDates.count {
                errorMessage = "存在重复的日期，请检查选择"
                showError = true
                return nil
            }

            validatedTargetFrequency = selectedOneTimeDates.count
        }
        
        return validatedTargetFrequency
    }
    
    /// 创建习惯对象
    /// - Parameter validatedFrequency: 验证后的目标频率
    /// - Returns: 配置完整的习惯对象
    private func createHabitObject(validatedFrequency: Int) -> EAHabit {
        let newHabit = EAHabit(
            name: habitName,
            iconName: selectedIcon,
            targetFrequency: validatedFrequency,
            preferredTimeSlot: localizedTimeSlot(selectedTimeSlot)
        )
        
        // 设置详细的频率配置
        configureHabitFrequency(habit: newHabit)
        
        // 设置提醒配置
        configureHabitReminders(habit: newHabit)
        
        // 设置其他属性
        newHabit.category = selectedCategory
        newHabit.difficulty = selectedDifficulty
        
        return newHabit
    }
    
    /// 配置习惯频率设置
    /// - Parameter habit: 要配置的习惯对象
    private func configureHabitFrequency(habit: EAHabit) {
        habit.frequencyType = selectedFrequencyType.rawValue
        habit.selectedWeekdays = Array(selectedWeekdays)
        habit.dailyTarget = dailyTarget
        habit.monthlyTarget = monthlyTarget
        habit.monthlyMode = monthlyMode.rawValue
        habit.selectedMonthlyDates = Array(selectedMonthlyDates)
        habit.selectedOneTimeDates = selectedOneTimeDates
    }
    
    /// 配置习惯提醒设置
    /// - Parameter habit: 要配置的习惯对象
    private func configureHabitReminders(habit: EAHabit) {
        if !reminderTimes.isEmpty {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "HH:mm"
            let reminderTimeStrings = reminderTimes.map { dateFormatter.string(from: $0) }
            habit.reminderTimes = reminderTimeStrings
            habit.reminderEnabled = true
        } else {
            habit.reminderTimes = []
            habit.reminderEnabled = false
        }
    }
    
    /// 保存习惯到数据库（遵循iOS 18+关系赋值顺序）
    /// - Parameters:
    ///   - habit: 要保存的习惯对象
    ///   - user: 关联的用户对象
    private func saveHabitToDatabase(habit: EAHabit, user: EAUser) async throws {
        guard let container = repositoryContainer else {
            throw NSError(domain: "EAHabitCreationViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "Repository容器未初始化"])
        }
        
        // 设置关系
        habit.user = user
        
        // 通过Repository保存
        try await container.habitRepository.saveHabit(habit)
    }
    
    /// 发送习惯创建通知
    /// - Parameter habitId: 创建的习惯ID
    private func notifyHabitCreated(habitId: UUID) {
        NotificationCenter.default.post(
            name: NSNotification.Name("HabitDataChanged"),
            object: habitId
        )
        NotificationCenter.default.post(
            name: NSNotification.Name("HabitCreated"),
            object: habitId
        )
    }
    
    /// 处理创建习惯的错误
    /// - Parameter error: 发生的错误
    private func handleCreateHabitError(_ error: Error) {
        isLoading = false
        showError = true

        // ✅ 增强错误处理：根据错误类型提供具体的用户友好信息
        if let repositoryError = error as? EARepositoryError {
            switch repositoryError {
            case .userNotFound:
                errorMessage = "用户信息丢失，请重新登录"
            case .contextMismatch:
                errorMessage = "数据同步错误，请重试"
            case .saveFailed:
                errorMessage = "保存失败，请检查网络连接后重试"
            default:
                errorMessage = "创建失败：\(repositoryError.localizedDescription)"
            }
        } else {
            let errorDescription = error.localizedDescription.lowercased()
            if errorDescription.contains("incompatible") &&
               errorDescription.contains("nsmanagedobjectmodel") {
                errorMessage = "数据模型需要更新，请重启应用后重试"
            } else if errorDescription.contains("network") {
                errorMessage = "网络连接错误，请检查网络后重试"
            } else if errorDescription.contains("memory") {
                errorMessage = "内存不足，请关闭其他应用后重试"
            } else if errorDescription.contains("timeout") {
                errorMessage = "操作超时，请重试"
            } else {
                errorMessage = "创建计划失败，请重试"
            }
        }

        // ✅ 性能优化：移除debug print语句，减少运行时开销
    }
}

// MARK: - 性能优化工具

/// 超时错误类型
struct TimeoutError: Error {
    let message: String

    init(_ message: String = "操作超时") {
        self.message = message
    }
}

/// 带超时的异步操作工具函数
func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
    return try await withThrowingTaskGroup(of: T.self) { group in
        // 添加主要操作任务
        group.addTask {
            try await operation()
        }

        // 添加超时任务
        group.addTask {
            try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
            throw TimeoutError("操作超时（\(seconds)秒）")
        }

        // 返回第一个完成的任务结果
        let result = try await group.next()!
        group.cancelAll()
        return result
    }
}

// MARK: - Supporting Types

struct TimeSlot {
    let id: String
    let name: String
    let description: String
}

// MARK: - 功能管理器集成示例

extension EAHabitCreationViewModel {
    
    /// 安全创建习惯（带功能检查）
    func createHabitSafely() async {
        let featureManager = EAFeatureManager()
        
        // 检查AI功能是否可用
        if featureManager.checkAIFeatures() {
            // AI功能可用，可以使用AI建议和分析
            await createHabitWithAISupport()
        } else {
            // AI功能不可用，使用基础创建流程
            await createHabitBasic()
            
            // 显示功能状态信息
            let statusMessage = featureManager.getAIStatusMessage()
            if !featureManager.isAIFeaturesAvailable {
                showUpgradePrompt(message: statusMessage)
            }
        }
    }
    
    /// 带AI支持的习惯创建
    private func createHabitWithAISupport() async {
        // 完整的AI功能实现
        await createHabit()
    }
    
    /// 基础习惯创建（无AI功能）
    private func createHabitBasic() async {
        // 基础创建流程，不依赖AI模型
        await createHabit()
    }
    
    /// 显示升级提示
    private func showUpgradePrompt(message: String) {
        // 显示用户友好的升级提示
        errorMessage = message
        showError = true
    }
    
    // MARK: - 星际能量奖励方法
    
    // ✅ iOS 18.5真机修复：移除复杂的星际能量奖励逻辑，改用Repository模式
} 
