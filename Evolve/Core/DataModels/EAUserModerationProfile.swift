//
//  EAUserModerationProfile.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData

/// 用户管理档案模型（MVP精简版）
/// 负责管理用户的基础管理数据，从EAUser模型中分离出来以降低复杂度
/// 遵循开发规范文档的"数据模型复杂度控制规范"
/// ✅ MVP版本：移除社区举报功能，保留基础用户管理
@Model
final class EAUserModerationProfile: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var lastUpdateDate: Date = Date()
    
    // MARK: - 核心关系
    
    /// 关联的用户（SwiftData标准关系）
    var user: EAUser?
    
    // MARK: - 管理统计属性（MVP精简版）
    
    /// 信誉分数（0-100）
    var reputationScore: Double = 100.0
    
    /// 是否被禁言
    var isMuted: Bool = false
    
    /// 禁言到期时间
    var muteExpiryDate: Date?
    
    /// 是否被封禁
    var isBanned: Bool = false
    
    /// 封禁到期时间
    var banExpiryDate: Date?
    
    /// 警告次数
    var warningCount: Int = 0
    
    /// 最后警告时间
    var lastWarningDate: Date?
    
    // MARK: - 初始化
    
    init() {
        // ✅ MVP精简版：移除社区举报相关初始化
    }
    
    // MARK: - 管理功能便捷方法（MVP精简版）
    
    /// 检查用户是否当前被禁言
    func isCurrentlyMuted() -> Bool {
        guard isMuted else { return false }
        if let expiryDate = muteExpiryDate {
            return Date() < expiryDate
        }
        return true // 永久禁言
    }
    
    /// 检查用户是否当前被封禁
    func isCurrentlyBanned() -> Bool {
        guard isBanned else { return false }
        if let expiryDate = banExpiryDate {
            return Date() < expiryDate
        }
        return true // 永久封禁
    }
    
    /// 更新管理统计数据（MVP精简版）
    func updateModerationStats() {
        lastUpdateDate = Date()
        // ✅ MVP版本：移除社区举报相关统计
    }
    
    /// 添加警告
    func addWarning() {
        warningCount += 1
        lastWarningDate = Date()
        reputationScore = max(0, reputationScore - 5.0)
        updateModerationStats()
    }
    
    /// 设置禁言
    func setMute(duration: TimeInterval? = nil) {
        isMuted = true
        if let duration = duration {
            muteExpiryDate = Date().addingTimeInterval(duration)
        } else {
            muteExpiryDate = nil // 永久禁言
        }
        reputationScore = max(0, reputationScore - 15.0)
        updateModerationStats()
    }
    
    /// 解除禁言
    func removeMute() {
        isMuted = false
        muteExpiryDate = nil
        updateModerationStats()
    }
    
    /// 设置封禁
    func setBan(duration: TimeInterval? = nil) {
        isBanned = true
        if let duration = duration {
            banExpiryDate = Date().addingTimeInterval(duration)
        } else {
            banExpiryDate = nil // 永久封禁
        }
        reputationScore = 0
        updateModerationStats()
    }
    
    /// 解除封禁
    func removeBan() {
        isBanned = false
        banExpiryDate = nil
        reputationScore = max(50.0, reputationScore) // 恢复基础信誉
        updateModerationStats()
    }
    
    // ✅ MVP版本：移除社区举报相关方法
    
    // ✅ MVP版本：移除社区内容相关权限检查方法
    
    // ✅ MVP版本：移除社区举报相关的安全操作方法
} 