//
//  EAUserSocialProfile.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData

/// 用户社交档案模型
/// 负责管理用户的社交关系数据，从EAUser模型中分离出来以降低复杂度
/// 遵循开发规范文档的"数据模型复杂度控制规范"
@Model
final class EAUserSocialProfile: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var lastUpdateDate: Date = Date()
    
    // MARK: - 核心关系
    
    /// 🔑 关键修复：关联的用户（SwiftData标准关系，必须使用普通属性与EAUser的inverse对应）
    /// ⚠️ 重要：此属性为普通属性，EAUser端定义@Relationship(inverse: \EAUserSocialProfile.user)
    /// 这是SwiftData单端inverse关系模式的正确实现
    var user: EAUser?
    
    // 🔑 MVP精简：移除所有社区、好友、屏蔽相关关系
    // 保留模型本身以维护与EAUser的关系，但清理内部复杂关系

    // ❌ 已删除：社区相关关系
    // - followers: [EACommunityFollow]
    // - posts: [EACommunityPost]

    // ❌ 已删除：好友功能关系
    // - initiatedFriendships: [EAFriendship]
    // - receivedFriendships: [EAFriendship]
    // - sentFriendRequests: [EAFriendRequest]
    // - receivedFriendRequests: [EAFriendRequest]

    // ❌ 已删除：社交活动档案
    // - socialActivity: EAUserSocialActivity?

    // ❌ 已删除：屏蔽功能关系
    // - initiatedBlocks: [EABlockedUser]
    // - receivedBlocks: [EABlockedUser]
    
    // MARK: - 社交统计属性
    
    /// 关注数量缓存
    var followingCount: Int = 0
    
    /// 粉丝数量缓存
    var followersCount: Int = 0
    
    /// 最后活跃时间
    var lastActiveDate: Date = Date()
    
    /// 社交活跃度分数
    var socialActivityScore: Double = 0.0

    // MARK: - 🔑 新增：骚扰防护和安全机制

    /// 🔑 修复：黑名单用户ID列表（使用字符串存储UUID，解决SwiftData兼容性问题）
    private var blockedUserIdsString: String = ""

    /// 🔑 修复：举报的用户ID列表（使用字符串存储UUID，解决SwiftData兼容性问题）
    private var reportedUserIdsString: String = ""

    /// 每日好友请求发送次数（重置时间：每日0点）
    var dailyFriendRequestCount: Int = 0

    /// 最后发送好友请求的日期
    var lastFriendRequestDate: Date?

    /// 每小时好友请求发送次数
    var hourlyFriendRequestCount: Int = 0

    /// 最后发送好友请求的小时
    var lastFriendRequestHour: Int?

    // MARK: - 🔑 计算属性：维护API兼容性

    /// 黑名单用户ID列表（计算属性，维护API兼容性）
    var blockedUserIds: [UUID] {
        get {
            guard !blockedUserIdsString.isEmpty else { return [] }
            return blockedUserIdsString.split(separator: ",")
                .compactMap { UUID(uuidString: String($0).trimmingCharacters(in: .whitespaces)) }
        }
        set {
            blockedUserIdsString = newValue.map { $0.uuidString }.joined(separator: ",")
        }
    }

    /// 举报的用户ID列表（计算属性，维护API兼容性）
    var reportedUserIds: [UUID] {
        get {
            guard !reportedUserIdsString.isEmpty else { return [] }
            return reportedUserIdsString.split(separator: ",")
                .compactMap { UUID(uuidString: String($0).trimmingCharacters(in: .whitespaces)) }
        }
        set {
            reportedUserIdsString = newValue.map { $0.uuidString }.joined(separator: ",")
        }
    }

    /// 🔑 关键修复：宇宙探索徽章列表（计算属性，维护API兼容性）
    var explorationBadges: [String]? {
        get {
            guard !explorationBadgesString.isEmpty else { return [] }
            return explorationBadgesString.split(separator: ",")
                .map { String($0).trimmingCharacters(in: .whitespaces) }
        }
        set {
            explorationBadgesString = newValue?.joined(separator: ",") ?? ""
        }
    }

    /// 🔑 关键修复：星际能量每日获得记录（计算属性，维护API兼容性）
    var dailyEnergyHistory: [String]? {
        get {
            guard !dailyEnergyHistoryString.isEmpty else { return [] }
            return dailyEnergyHistoryString.split(separator: ",")
                .map { String($0).trimmingCharacters(in: .whitespaces) }
        }
        set {
            dailyEnergyHistoryString = newValue?.joined(separator: ",") ?? ""
        }
    }

    /// 🔑 关键修复：探索里程碑达成日期记录（计算属性，维护API兼容性）
    var explorationMilestones: [String]? {
        get {
            guard !explorationMilestonesString.isEmpty else { return [] }
            return explorationMilestonesString.split(separator: ",")
                .map { String($0).trimmingCharacters(in: .whitespaces) }
        }
        set {
            explorationMilestonesString = newValue?.joined(separator: ",") ?? ""
        }
    }
    
    // MARK: - 数字宇宙星际探索者属性（兼容性扩展，所有属性为可选类型）
    
    /// 星际等级 (1-20级) - 可选类型确保兼容性
    var stellarLevel: Int? = nil
    
    /// 星际能量总数 - 可选类型确保兼容性
    var totalStellarEnergy: Int? = nil
    
    /// 探索者称号 (新手探索者、星际旅者、宇宙守护者等) - 可选类型确保兼容性
    var explorerTitle: String? = nil
    
    /// 所属宇宙区域 (银河系、仙女座、猎户座等) - 可选类型确保兼容性
    var universeRegion: String? = nil
    
    /// 🔑 关键修复：宇宙探索徽章列表（使用字符串存储，解决SwiftData兼容性问题）
    private var explorationBadgesString: String = ""

    /// 🔑 关键修复：星际能量每日获得记录（使用字符串存储，解决SwiftData兼容性问题）
    private var dailyEnergyHistoryString: String = ""

    /// 宇宙贡献值 (分享内容、帮助他人等获得) - 可选类型确保兼容性
    var cosmicContribution: Int? = nil

    /// 🔑 关键修复：探索里程碑达成日期记录（使用字符串存储，解决SwiftData兼容性问题）
    private var explorationMilestonesString: String = ""
    
    /// ✅ 新增：最后星际能量更新时间 - 用于星际能量计算和连续活跃奖励
    var lastEnergyUpdateDate: Date? = nil
    
    /// ✅ 新增：等级提升时间 - 用于记录等级提升里程碑
    var levelUpDate: Date? = nil
    
    // MARK: - 初始化
    
    init() {
        // 🔑 MVP精简：移除所有社区、好友、屏蔽相关的初始化
        // 保持模型简洁，只保留基本属性的默认值
        // 所有复杂关系已在MVP精简中移除
    }

    // 🔑 MVP精简：移除社交活动档案相关方法
    // ensureSocialActivity() 方法已删除，因为socialActivity关系已移除
    
    // MARK: - 社交功能便捷方法
    
    /// 获取用户关注的人数（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchUserFollowing(userId:) 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchUserFollowing(userId:) 替代")
    func getFollowingCount() -> Int {
        return 0 // 返回默认值，实际需要通过Repository查询
    }
    
    // 🔑 MVP精简：移除所有社区相关方法
    // 以下方法已删除，因为相关关系已移除：
    // - getFollowersCount()
    // - getVisiblePostsCount()
    // - isFollowing(userId:)
    // - isFollowedBy(userId:)
    
    /// 更新社交统计数据（MVP精简版）
    func updateSocialStats() {
        // 🔑 MVP精简：简化统计逻辑，移除复杂的社区功能
        lastUpdateDate = Date()
        // 重置社交活跃度分数为0，因为相关功能已移除
        socialActivityScore = 0.0
    }
    
    /// 获取互相关注的用户（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchMutualFollows 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchMutualFollows 替代")
    func getMutualFollows() -> [EACommunityFollow] {
        return [] // 返回空数组，实际需要通过Repository查询
    }
    
    /// 获取最近关注的用户（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchUserFollowing 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchUserFollowing 替代")
    func getRecentFollowing(limit: Int = 10) -> [EACommunityFollow] {
        return [] // 返回空数组，实际需要通过Repository查询
    }
    
    /// 获取最近的粉丝
    func getRecentFollowers(limit: Int = 10) -> [EACommunityFollow] {
        return followers
            .filter { $0.isActive }
            .sorted { $0.creationDate > $1.creationDate }
            .prefix(limit)
            .map { $0 }
    }
    
    /// 获取最近的帖子（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchUserPosts(userId:) 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchUserPosts(userId:) 替代")
    func getRecentPosts(limit: Int = 10) -> [EACommunityPost] {
        return [] // 返回空数组，实际需要通过Repository查询
    }
    
    // MARK: - 安全关系操作方法
    
    /// 安全添加关注关系（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.createFollow 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.createFollow 替代")
    @MainActor
    func safelyAddFollowing(_ follow: EACommunityFollow, in context: ModelContext) throws {
        // 方法已弃用，不执行任何操作
        throw DataModelError.methodDeprecated
    }
    
    /// 安全添加粉丝关系
    @MainActor
    func safelyAddFollower(_ follow: EACommunityFollow, in context: ModelContext) throws {
        guard follow.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.followers.append(follow)
        updateSocialStats()
        try context.save()
    }
    
    /// 安全添加帖子（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.createPost 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.createPost 替代")
    @MainActor
    func safelyAddPost(_ post: EACommunityPost, in context: ModelContext) throws {
        // 方法已弃用，不执行任何操作
        throw DataModelError.methodDeprecated
    }
    
    // MARK: - 数字宇宙星际系统方法
    
    /// 🚨 关键修复：轻量级同步初始化（避免主线程阻塞）
    /// 只设置基础属性，复杂数组操作延迟到后台线程
    func initializeDigitalUniverseData() {
        // 🚨 关键修复：多重防护机制，防止重复初始化和递归调用
        guard stellarLevel == nil else {
            return
        }

        // 🔑 轻量级设置：只设置基础属性，避免触发复杂计算属性
        stellarLevel = 1
        totalStellarEnergy = 0
        explorerTitle = "新手探索者"
        universeRegion = "银河系边缘"
        cosmicContribution = 0
        lastEnergyUpdateDate = Date()

        // 🚨 关键修复：直接设置字符串属性，避免触发复杂计算属性
        explorationBadgesString = ""
        dailyEnergyHistoryString = ""
        explorationMilestonesString = ""
    }

    /// 🔑 新增：异步完整初始化（在后台线程执行复杂操作）
    @MainActor
    func initializeDigitalUniverseDataAsync() async {
        // 确保基础数据已初始化
        if stellarLevel == nil {
            initializeDigitalUniverseData()
        }

        // 🔑 在主线程安全执行数组初始化
        // 只有在需要时才触发计算属性
        if explorationBadges == nil {
            explorationBadges = []
        }
        if dailyEnergyHistory == nil {
            dailyEnergyHistory = []
        }
        if explorationMilestones == nil {
            explorationMilestones = []
        }
    }
    
    /// 获得星际能量
    func gainStellarEnergy(_ amount: Int) {
        // 确保数字宇宙数据已初始化
        initializeDigitalUniverseData()
        
        let currentEnergy = totalStellarEnergy ?? 0
        totalStellarEnergy = currentEnergy + amount
        recordDailyEnergyChange(amount, reason: "习惯完成")
        checkLevelUp()
    }
    
    /// ✅ 新增：记录每日能量变化（支持增加和减少）
    func recordDailyEnergyChange(_ amount: Int, reason: String) {
        initializeDigitalUniverseData()
        
        let today = ISO8601DateFormatter().string(from: Date())
        var history = dailyEnergyHistory ?? []
        
        // 记录格式：日期:变化量:原因
        let record = "\(today):\(amount):\(reason)"
        history.append(record)
        
        // 保留最近30天的记录
        if history.count > 30 {
            history.removeFirst()
        }
        dailyEnergyHistory = history
        
        // 如果是正数，检查升级
        if amount > 0 {
            checkLevelUp()
        }
    }
    
    /// 记录每日能量获得（保持向后兼容）
    private func recordDailyEnergyGain(_ amount: Int) {
        recordDailyEnergyChange(amount, reason: "习惯完成")
    }
    
    /// 检查是否应该升级星际等级
    private func checkLevelUp() {
        let newLevel = calculateStellarLevel()
        let currentLevel = stellarLevel ?? 1
        if newLevel > currentLevel {
            stellarLevel = newLevel
            updateExplorerTitle()
        }
    }
    
    /// 根据星际能量计算等级
    private func calculateStellarLevel() -> Int {
        // 星际等级算法：每1000能量升1级，最高20级
        let energy = totalStellarEnergy ?? 0
        let baseLevel = min(energy / 1000, 19) + 1
        return baseLevel
    }
    
    /// 更新探索者称号
    private func updateExplorerTitle() {
        let currentLevel = stellarLevel ?? 1
        switch currentLevel {
        case 1...3:
            explorerTitle = "新手探索者"
        case 4...7:
            explorerTitle = "星际旅者"
        case 8...12:
            explorerTitle = "宇宙航行者"
        case 13...16:
            explorerTitle = "星系守护者"
        case 17...20:
            explorerTitle = "宇宙大师"
        default:
            explorerTitle = "传奇探索者"
        }
    }
    
    /// 添加探索徽章
    func addExplorationBadge(_ badgeId: String) {
        initializeDigitalUniverseData()
        
        var badges = explorationBadges ?? []
        if !badges.contains(badgeId) {
            badges.append(badgeId)
        }
        explorationBadges = badges
    }
    
    /// 记录探索里程碑
    func recordExplorationMilestone(_ milestone: String) {
        initializeDigitalUniverseData()
        
        let timestamp = ISO8601DateFormatter().string(from: Date())
        var milestones = explorationMilestones ?? []
        milestones.append("\(timestamp):\(milestone)")
        explorationMilestones = milestones
    }
    
    /// 获取连续活跃天数
    func getConsecutiveActiveDays() -> Int {
        guard let history = dailyEnergyHistory else { return 0 }
        
        let dateFormatter = ISO8601DateFormatter()
        let today = Date()
        var consecutiveDays = 0
        
        // 从最近的记录往前计算连续天数
        for record in history.reversed() {
            guard let colonIndex = record.firstIndex(of: ":"),
                  let date = dateFormatter.date(from: String(record[..<colonIndex])) else {
                continue
            }
            
            let daysDiff = Calendar.current.dateComponents([.day], from: date, to: today).day ?? 0
            if daysDiff == consecutiveDays {
                consecutiveDays += 1
            } else {
                break
            }
        }
        
        return consecutiveDays
    }
    
    /// 计算星际影响力 (综合社交和宇宙数据)
    func calculateStellarInfluence() -> Double {
        let socialScore = socialActivityScore
        let energyScore = Double(totalStellarEnergy ?? 0) / 100.0
        let levelBonus = Double(stellarLevel ?? 1) * 10.0
        let contributionScore = Double(cosmicContribution ?? 0)

        return socialScore + energyScore + levelBonus + contributionScore
    }

    // MARK: - 好友功能便捷方法（星际伙伴系统）

    /// 🔑 修复：获取所有有效的好友 - 需要通过Repository实现
    /// 由于移除了直接关系属性，此方法需要通过FriendshipRepository来实现
    /*
    func getAllFriends() -> [EAUserSocialProfile] {
        // 此方法需要通过Repository查询实现
        // 不能直接访问已移除的关系属性
        return []
    }
    */

    /// 🔑 修复：检查是否与某个用户是好友 - 需要通过Repository实现
    /// 由于移除了直接关系属性，此方法需要通过FriendshipRepository来实现
    /*
    func isFriend(with userProfile: EAUserSocialProfile) -> Bool {
        return getFriendship(with: userProfile) != nil
    }

    /// 获取与某个用户的好友关系 - 需要通过Repository实现
    func getFriendship(with userProfile: EAUserSocialProfile) -> EAFriendship? {
        // 此方法需要通过Repository查询实现
        // 不能直接访问已移除的关系属性
        return nil
    }
    */

    /// 获取待处理的好友请求数量
    func getPendingFriendRequestsCount() -> Int {
        return receivedFriendRequests.filter { $0.status == .pending && !$0.isExpired() }.count
    }

    /// 获取未读消息数量
    /// 注意：此方法已弃用，请使用 EAFriendMessageRepository.countUnreadMessages(for:) 替代
    @available(*, deprecated, message: "使用 EAFriendMessageRepository.countUnreadMessages(for:) 替代")
    func getUnreadMessagesCount() -> Int {
        // ✅ 修复：不再直接访问receivedMessages，返回0并提示使用Repository
        // 实际的未读消息统计应该通过 EAFriendMessageRepository.countUnreadMessages(for:) 获取
        return 0  // 返回默认值，强制使用Repository模式
    }

    /// 检查是否可以向某个用户发送好友请求
    func canSendFriendRequest(to userProfile: EAUserSocialProfile) -> Bool {
        #if DEBUG
        print("🔍 [UserSocialProfile] canSendFriendRequest检查开始")
        print("🔍 [UserSocialProfile] 当前用户ID: \(self.id), 目标用户ID: \(userProfile.id)")
        #endif

        // 不能向自己发送请求
        if userProfile.id == self.id {
            #if DEBUG
            print("❌ [UserSocialProfile] 不能向自己发送请求")
            #endif
            return false
        }

        // 已经是好友了
        if false { // TODO: 需要重新实现好友检查逻辑
            #if DEBUG
            print("❌ [UserSocialProfile] 已经是好友")
            #endif
            return false
        }

        // 🔑 骚扰防护：检查是否被对方拉黑
        if userProfile.blockedUserIds.contains(self.id) {
            #if DEBUG
            print("❌ [UserSocialProfile] 被对方拉黑")
            #endif
            return false
        }

        // 🔑 骚扰防护：检查是否在冷却期内
        if let lastRequest = getLastFriendRequest(to: userProfile), lastRequest.isInCooldown() {
            #if DEBUG
            print("❌ [UserSocialProfile] 在冷却期内")
            #endif
            return false
        }

        // 🔑 频率限制：检查每日发送次数
        if !canSendMoreRequestsToday() {
            #if DEBUG
            print("❌ [UserSocialProfile] 今日发送次数已达上限")
            #endif
            return false
        }

        // 🔑 频率限制：检查每小时发送次数
        if !canSendMoreRequestsThisHour() {
            #if DEBUG
            print("❌ [UserSocialProfile] 本小时发送次数已达上限")
            #endif
            return false
        }

        // 检查是否已经发送过待处理的请求
        let hasPendingRequest = sentFriendRequests.contains { request in
            request.receiverProfile?.id == userProfile.id &&
            request.status == .pending &&
            !request.isExpired()
        }

        #if DEBUG
        print("🔍 [UserSocialProfile] sentFriendRequests数量: \(sentFriendRequests.count)")
        print("🔍 [UserSocialProfile] hasPendingRequest: \(hasPendingRequest)")
        #endif

        let result = !hasPendingRequest

        #if DEBUG
        print("✅ [UserSocialProfile] canSendFriendRequest最终结果: \(result)")
        #endif

        return result
    }

    /// 检查是否可以向某个用户发送消息
    func canSendMessage(to userProfile: EAUserSocialProfile) -> Bool {
        // 只有确认好友才能发送消息（核心安全机制）
        return false // TODO: 需要重新实现好友检查逻辑
    }

    /// 获取最近的聊天记录
    func getRecentChats(limit: Int = 10) -> [EAFriendship] {
        // TODO: 需要重新实现获取所有好友关系的逻辑
        return []
            .filter { $0.lastMessageDate != nil }
            .sorted { ($0.lastMessageDate ?? Date.distantPast) > ($1.lastMessageDate ?? Date.distantPast) }
            .prefix(limit)
            .map { $0 }
    }

    /// 更新好友统计数据
    func updateFriendStats() {
        // 这里可以添加好友相关的统计更新逻辑
        // 例如：更新好友数量、最后互动时间等
        lastUpdateDate = Date()
    }

    // MARK: - 🔑 骚扰防护辅助方法

    /// 获取向某个用户发送的最后一个好友请求
    func getLastFriendRequest(to userProfile: EAUserSocialProfile) -> EAFriendRequest? {
        return sentFriendRequests
            .filter { $0.receiverProfile?.id == userProfile.id }
            .sorted { $0.creationDate > $1.creationDate }
            .first
    }

    /// 检查今天是否还能发送更多好友请求（每日限制5个）
    func canSendMoreRequestsToday() -> Bool {
        let today = Calendar.current.startOfDay(for: Date())

        // 如果是新的一天，重置计数
        if let lastDate = lastFriendRequestDate,
           Calendar.current.startOfDay(for: lastDate) < today {
            dailyFriendRequestCount = 0
        }

        return dailyFriendRequestCount < 5
    }

    /// 检查这个小时是否还能发送更多好友请求（每小时限制2个）
    func canSendMoreRequestsThisHour() -> Bool {
        let currentHour = Calendar.current.component(.hour, from: Date())

        // 如果是新的小时，重置计数
        if lastFriendRequestHour != currentHour {
            hourlyFriendRequestCount = 0
        }

        return hourlyFriendRequestCount < 2
    }

    /// 记录好友请求发送（更新频率限制计数）
    func recordFriendRequestSent() {
        let now = Date()
        let currentHour = Calendar.current.component(.hour, from: now)
        let today = Calendar.current.startOfDay(for: now)

        // 更新每日计数
        if let lastDate = lastFriendRequestDate,
           Calendar.current.startOfDay(for: lastDate) < today {
            dailyFriendRequestCount = 1
        } else {
            dailyFriendRequestCount += 1
        }

        // 更新每小时计数
        if lastFriendRequestHour != currentHour {
            hourlyFriendRequestCount = 1
        } else {
            hourlyFriendRequestCount += 1
        }

        lastFriendRequestDate = now
        lastFriendRequestHour = currentHour
    }

    /// 拉黑用户
    func blockUser(_ userId: UUID) {
        if !blockedUserIds.contains(userId) {
            blockedUserIds.append(userId)
        }
    }

    /// 取消拉黑用户
    func unblockUser(_ userId: UUID) {
        blockedUserIds.removeAll { $0 == userId }
    }

    /// 举报用户
    func reportUser(_ userId: UUID) {
        if !reportedUserIds.contains(userId) {
            reportedUserIds.append(userId)
        }
    }

    /// 检查是否已拉黑某用户
    func isUserBlocked(_ userId: UUID) -> Bool {
        return blockedUserIds.contains(userId)
    }

    /// 检查是否已举报某用户
    func isUserReported(_ userId: UUID) -> Bool {
        return reportedUserIds.contains(userId)
    }
}