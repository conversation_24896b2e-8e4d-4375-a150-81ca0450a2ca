//
//  EARepositoryFactory.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData

/// Repository工厂类
/// 负责创建和管理各种Repository实例，确保依赖注入的一致性
@MainActor
final class EARepositoryFactory {
    
    // ✅ MVP精简：移除社区Repository创建方法，专注于核心功能
    
    // TODO: 其他Repository待实现
    // - EAUserRepository
    // - EAHabitRepository  
    // - EACompletionRepository
    // 等等...
} 