//
//  EASharingMomentDetectionService.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//  Phase 2 Day 4: AI增强功能开发 - 智能分享时机检测
//

import Foundation
import SwiftUI

/// 智能分享时机检测服务
/// 基于本地规则引擎+AI确认的混合策略，实现成本控制的分享时机检测
/// 遵循开发规范文档的"AI成本控制开发规范"和"Repository模式强制执行规范"
@MainActor
class EASharingMomentDetectionService: ObservableObject {
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    // ✅ MVP精简：移除社区AI数据桥接依赖
    
    // MARK: - 缓存管理
    
    private var detectionCache: [UUID: (result: EASharingMoment?, timestamp: Date)] = [:]
    private let cacheValidDuration: TimeInterval = 24 * 3600 // 24小时缓存
    
    // MARK: - 本地规则引擎配置
    
    private let milestoneThresholds = [
        7, 14, 21, 30, 50, 75, 100, 150, 200, 365  // 重要里程碑天数
    ]
    
    private let streakBoostThresholds = [
        3, 5, 7, 14, 21, 30  // 连击奖励触发点
    ]
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
        // ✅ MVP精简：移除AI数据桥接依赖
    }
    
    // MARK: - 智能分享时机检测接口
    
    /// 检测用户是否应该分享当前习惯成就
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - habitId: 习惯ID
    /// - Returns: 分享建议（如果有）
    func detectSharingMoment(userId: UUID, habitId: UUID) async -> EASharingMoment? {
        // 检查缓存
        let cacheKey = userId
        if let cached = detectionCache[cacheKey],
           Date().timeIntervalSince(cached.timestamp) < cacheValidDuration {
            return cached.result
        }
        
        // 步骤1：本地规则引擎检测
        let localDetection = await performLocalDetection(userId: userId, habitId: habitId)
        
        guard let localMoment = localDetection else {
            // 无本地触发，直接返回nil
            detectionCache[cacheKey] = (nil, Date())
            return nil
        }
        
        // 步骤2：AI增强分析（仅在本地检测通过后调用）
        let enhancedMoment = await enhanceWithAI(localMoment: localMoment, userId: userId)
        
        // 更新缓存
        detectionCache[cacheKey] = (enhancedMoment, Date())
        
        return enhancedMoment
    }
    
    // MARK: - 本地规则引擎
    
    /// 执行本地规则检测
    private func performLocalDetection(userId: UUID, habitId: UUID) async -> EASharingMoment? {
        // 通过Repository获取用户习惯数据
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let habit = user?.habits.first(where: { $0.id == habitId }) else {
            return nil
        }
        
        // 获取习惯完成记录
        let allCompletions = habit.completions.sorted { $0.date > $1.date }
        
        // 规则1：里程碑检测
        if let milestone = detectMilestone(completions: allCompletions) {
            return EASharingMoment(
                type: .milestone,
                habitName: habit.name,
                achievementDescription: milestone.description,
                daysCount: milestone.daysCount,
                confidenceScore: 0.9,
                suggestedContent: milestone.suggestedContent,
                detectionReason: "达成\(milestone.daysCount)天习惯里程碑"
            )
        }
        
        // 规则2：连击奖励检测
        if let streak = detectStreakBoost(completions: allCompletions) {
            return EASharingMoment(
                type: .streakBoost,
                habitName: habit.name,
                achievementDescription: streak.description,
                daysCount: streak.daysCount,
                confidenceScore: 0.8,
                suggestedContent: streak.suggestedContent,
                detectionReason: "连续\(streak.daysCount)天完成习惯"
            )
        }
        
        // 规则3：个人最佳记录检测
        if let personalBest = detectPersonalBest(habit: habit, completions: allCompletions) {
            return EASharingMoment(
                type: .personalBest,
                habitName: habit.name,
                achievementDescription: personalBest.description,
                daysCount: personalBest.daysCount,
                confidenceScore: 0.85,
                suggestedContent: personalBest.suggestedContent,
                detectionReason: "刷新个人最佳记录"
            )
        }
        
        return nil
    }
    
    /// 检测里程碑成就
    private func detectMilestone(completions: [EACompletion]) -> (description: String, daysCount: Int, suggestedContent: String)? {
        let totalDays = completions.count
        
        // 检查是否刚好达到里程碑
        guard milestoneThresholds.contains(totalDays) else { return nil }
        
        let description = "🎉 恭喜达成\(totalDays)天习惯养成里程碑！"
        let suggestedContent = generateMilestoneContent(days: totalDays)
        
        return (description, totalDays, suggestedContent)
    }
    
    /// 检测连击奖励
    private func detectStreakBoost(completions: [EACompletion]) -> (description: String, daysCount: Int, suggestedContent: String)? {
        let streakDays = calculateCurrentStreak(completions: completions)
        
        // 检查是否刚好达到连击阈值
        guard streakBoostThresholds.contains(streakDays) else { return nil }
        
        let description = "🔥 连续\(streakDays)天坚持，能量满满！"
        let suggestedContent = generateStreakContent(days: streakDays)
        
        return (description, streakDays, suggestedContent)
    }
    
    /// 检测个人最佳记录
    private func detectPersonalBest(habit: EAHabit, completions: [EACompletion]) -> (description: String, daysCount: Int, suggestedContent: String)? {
        let currentStreak = calculateCurrentStreak(completions: completions)
        let previousBest = calculatePreviousBestStreak(completions: completions)
        
        // 判断是否刷新个人记录
        guard currentStreak > previousBest && currentStreak >= 3 else { return nil }
        
        let description = "🏆 刷新个人最佳记录：连续\(currentStreak)天！"
        let suggestedContent = generatePersonalBestContent(habit: habit, days: currentStreak, previousBest: previousBest)
        
        return (description, currentStreak, suggestedContent)
    }
    
    // MARK: - AI增强分析
    
    /// 使用AI增强本地检测结果
    private func enhanceWithAI(localMoment: EASharingMoment, userId: UUID) async -> EASharingMoment? {
        // ✅ MVP精简：移除社区AI数据桥接依赖
        // 直接返回本地检测结果，专注于核心功能
        return localMoment
    }
    
    // ✅ MVP精简：移除社区相关的AI增强方法
    
    // MARK: - 辅助计算方法
    
    /// 计算当前连击天数
    private func calculateCurrentStreak(completions: [EACompletion]) -> Int {
        let sortedCompletions = completions.sorted { $0.date > $1.date }
        var streakCount = 0
        let calendar = Calendar.current
        
        for (index, completion) in sortedCompletions.enumerated() {
            if index == 0 {
                // 第一个记录，检查是否是今天或昨天
                let daysDiff = calendar.dateComponents([.day], from: completion.date, to: Date()).day ?? 0
                if daysDiff <= 1 {
                    streakCount = 1
                } else {
                    break
                }
            } else {
                // 检查与前一个记录是否连续
                let previousCompletion = sortedCompletions[index - 1]
                let daysDiff = calendar.dateComponents([.day], from: completion.date, to: previousCompletion.date).day ?? 0
                if daysDiff == 1 {
                    streakCount += 1
                } else {
                    break
                }
            }
        }
        
        return streakCount
    }
    
    /// 计算历史最佳连击记录
    private func calculatePreviousBestStreak(completions: [EACompletion]) -> Int {
        let sortedCompletions = completions.sorted { $0.date < $1.date }
        var maxStreak = 0
        var currentStreak = 0
        let calendar = Calendar.current
        
        for (index, completion) in sortedCompletions.enumerated() {
            if index == 0 {
                currentStreak = 1
            } else {
                let previousCompletion = sortedCompletions[index - 1]
                let daysDiff = calendar.dateComponents([.day], from: previousCompletion.date, to: completion.date).day ?? 0
                if daysDiff == 1 {
                    currentStreak += 1
                } else {
                    maxStreak = max(maxStreak, currentStreak)
                    currentStreak = 1
                }
            }
        }
        
        return max(maxStreak, currentStreak)
    }
    
    // MARK: - 内容生成辅助方法
    
    /// 生成里程碑分享内容
    private func generateMilestoneContent(days: Int) -> String {
        switch days {
        case 7:
            return "一周的坚持，是改变的开始！💪"
        case 30:
            return "一个月的努力，习惯开始成型！🌟"
        case 100:
            return "百日坚持，证明了自己的决心！🏆"
        case 365:
            return "一整年的坚持，这就是传奇！👑"
        default:
            return "\(days)天的坚持，每一天都在成长！✨"
        }
    }
    
    /// 生成连击分享内容
    private func generateStreakContent(days: Int) -> String {
        switch days {
        case 3:
            return "三天连击，势头正好！🔥"
        case 7:
            return "一周连击，状态火热！⚡"
        case 21:
            return "三周连击，习惯成自然！🌊"
        default:
            return "\(days)天连击，停不下来的节奏！🚀"
        }
    }
    
    /// 生成个人最佳记录分享内容
    private func generatePersonalBestContent(habit: EAHabit, days: Int, previousBest: Int) -> String {
        return "突破自我！\(habit.name)连续\(days)天，超越了之前\(previousBest)天的记录！🏆"
    }
}

// MARK: - 分享时机数据模型

/// 分享时机检测结果
struct EASharingMoment {
    let type: SharingMomentType
    let habitName: String
    let achievementDescription: String
    let daysCount: Int
    let confidenceScore: Double
    let suggestedContent: String
    let detectionReason: String
}

/// 分享时机类型
enum SharingMomentType {
    case milestone      // 里程碑成就
    case streakBoost    // 连击奖励
    case personalBest   // 个人最佳
    case weeklyGoal     // 周目标达成
    case emotionalHigh  // 情绪高点
} 