import SwiftData
import Foundation

// MARK: - 数据模型迁移管理

enum EAModelMigration {
    
    // MARK: - 当前Schema版本（与AppEntry.swift完全一致）

    static var currentSchema: Schema {
        Schema([
            // 核心用户模型
            EAUser.self,
            EAUserSettings.self,
            EAHabit.self,
            EACompletion.self,

            // 用户档案模型（MVP版本保留基础档案）
            EAUserSocialProfile.self,
            EAUserModerationProfile.self,
            EAUserDataProfile.self,
            EAUserAuthInfo.self,

            // AI和支付模型
            EAAIMessage.self,
            EAPayment.self,
            EAAnalytics.self,
            EAContent.self,
            EAPath.self,

            // ✅ MVP精简：移除宇宙挑战模型
        ])
    }
    
    // MARK: - 迁移策略
    
    static func performMigrationIfNeeded() throws {
        // 检查是否需要数据迁移
        // 当前版本为v3.2，暂时不需要复杂的迁移逻辑
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
    }
    
    // MARK: - 数据验证
    
    static func validateDataIntegrity() throws {
        // 验证数据完整性
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
    }
}

// MARK: - SwiftData Schema版本管理（2025年修正版 - 临时简化）

/// Schema版本5：完整版（包含好友功能，与AppEntry.swift一致）
enum EvolveSchemaV5: VersionedSchema {
    static var versionIdentifier = Schema.Version(5, 0, 0)

    static var models: [any PersistentModel.Type] {
        [
            // 核心用户模型
            EAUser.self,
            EAUserSettings.self,
            EAHabit.self,
            EACompletion.self,

            // 用户档案模型（MVP版本保留基础档案）
            EAUserSocialProfile.self,
            EAUserModerationProfile.self,
            EAUserDataProfile.self,
            EAUserAuthInfo.self,

            // AI和支付模型
            EAAIMessage.self,
            EAPayment.self,
            EAAnalytics.self,
            EAContent.self,
            EAPath.self,

            // ✅ MVP精简：移除宇宙挑战模型
        ]
    }
}

/// 迁移计划（完整版，支持好友功能）
enum EvolveModelMigrationPlan: SchemaMigrationPlan {
    static var schemas: [any VersionedSchema.Type] {
        [EvolveSchemaV5.self]
    }

    static var stages: [MigrationStage] {
        // 🚨 关键修复：强制重建数据库以支持好友功能
        // 由于Schema变化较大，使用全新Schema重建
        []
    }
}

// MARK: - ModelContainer扩展（2025年修正版）

extension ModelContainer {
    /// 🚨 关键修复：创建完整的兼容容器（包含好友功能）
    static func createCompatibleContainer() -> ModelContainer {
        do {
            let schema = Schema([
                // 核心用户模型
                EAUser.self,
                EAUserSettings.self,
                EAHabit.self,
                EACompletion.self,

                // 用户档案模型（MVP版本保留基础档案）
                EAUserSocialProfile.self,
                EAUserModerationProfile.self,
                EAUserDataProfile.self,
                EAUserAuthInfo.self,

                // AI和支付模型
                EAAIMessage.self,
                EAPayment.self,
                EAAnalytics.self,
                EAContent.self,
                EAPath.self,

                // ✅ MVP精简：移除宇宙挑战模型
            ])
            
            let configuration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: false,
                allowsSave: true,
                cloudKitDatabase: .none
            )
            
            let container = try ModelContainer(
                for: schema,
                configurations: [configuration]
            )
            
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            return container
            
        } catch {
            // 创建ModelContainer失败，静默处理
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            return createFallbackContainer()
        }
    }
    
    /// 🚨 关键修复：创建完整的降级容器（内存模式，包含好友功能）
    private static func createFallbackContainer() -> ModelContainer {
        do {
            let schema = Schema([
                // 核心用户模型
                EAUser.self,
                EAUserSettings.self,
                EAHabit.self,
                EACompletion.self,

                // 用户档案模型（MVP版本保留基础档案）
                EAUserSocialProfile.self,
                EAUserModerationProfile.self,
                EAUserDataProfile.self,
                EAUserAuthInfo.self,

                // AI和支付模型
                EAAIMessage.self,
                EAPayment.self,
                EAAnalytics.self,
                EAContent.self,
                EAPath.self,

                // ✅ MVP精简：移除宇宙挑战模型
            ])
            
            let configuration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: true
            )
            
            let container = try ModelContainer(
                for: schema,
                configurations: [configuration]
            )
            
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            return container
            
        } catch {
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            _ = error
            fatalError("无法创建任何ModelContainer")
        }
    }
}

// MARK: - 数据库重置管理器（2025年修正版）

class EADatabaseResetManager {
    init() {}
    
    private let currentSchemaVersion = "5.0.0_FriendSupport"
    private let schemaVersionKey = "EASchemaVersion"
    private let forceResetKey = "EAForceReset_2025_Friends"
    
    /// 检查是否需要重置数据库
    func resetDatabaseIfNeeded() {
        let savedVersion = UserDefaults.standard.string(forKey: schemaVersionKey)
        let forceReset = UserDefaults.standard.bool(forKey: forceResetKey)
        
        // 强制重置（开发环境）
        #if DEBUG
        if !forceReset {
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            forceResetDatabase()
            UserDefaults.standard.set(true, forKey: forceResetKey)
            return
        }
        #endif
        
        // 版本不匹配时重置
        if savedVersion != currentSchemaVersion {
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            _ = savedVersion
            forceResetDatabase()
        }
    }
    
    /// 强制重置数据库
    func forceResetDatabase() {
        let fileManager = FileManager.default
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, 
                                                  in: .userDomainMask).first else { 
            // 无法访问应用支持目录
            return 
        }
        
        let storeFiles = ["default.store", "default.store-wal", "default.store-shm"]
        
        for fileName in storeFiles {
            let fileURL = appSupportURL.appendingPathComponent(fileName)
            if fileManager.fileExists(atPath: fileURL.path) {
                do {
                    try fileManager.removeItem(at: fileURL)
                    // 🔑 生产环境：移除调试代码，符合.cursorrules规范
                    _ = fileName
                } catch {
                    // 🔑 生产环境：移除调试代码，符合.cursorrules规范
                    _ = fileName
                    _ = error
                }
            }
        }
        
        // 更新版本标记
        UserDefaults.standard.set(currentSchemaVersion, forKey: schemaVersionKey)
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
    }
    
    /// 获取当前Schema版本
    func getCurrentSchemaVersion() -> String {
        return currentSchemaVersion
    }
    
    /// 打印数据库状态（调试用）
    func printDatabaseStatus() {
        let savedVersion = UserDefaults.standard.string(forKey: schemaVersionKey)
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
        _ = savedVersion
        _ = currentSchemaVersion
        _ = UserDefaults.standard.bool(forKey: forceResetKey)
    }
}

// MARK: - 开发环境数据库管理工具

#if DEBUG
extension EADatabaseResetManager {
    /// 开发环境：强制重置数据库（用于Schema修正）
    func devForceResetForSchemaFix() {
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
        forceResetDatabase()
        
        // 清除所有相关标记
        UserDefaults.standard.removeObject(forKey: forceResetKey)
        UserDefaults.standard.removeObject(forKey: "EADatabaseInitialized")
        
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
    }
    
    /// 开发环境：打印详细数据库信息
    func devPrintDetailedStatus() {
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
        printDatabaseStatus()
        
        let fileManager = FileManager.default
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, 
                                                  in: .userDomainMask).first else { return }
        
        let storeFiles = ["default.store", "default.store-wal", "default.store-shm"]
        
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
        for fileName in storeFiles {
            let fileURL = appSupportURL.appendingPathComponent(fileName)
            let exists = fileManager.fileExists(atPath: fileURL.path)
            // 🔑 生产环境：移除调试代码，符合.cursorrules规范
            _ = fileName
            _ = exists
            
            if exists {
                do {
                    let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
                    let size = attributes[.size] as? Int64 ?? 0
                    let modificationDate = attributes[.modificationDate] as? Date
                    // 🔑 生产环境：移除调试代码，符合.cursorrules规范
                    _ = size
                    _ = modificationDate
                } catch {
                    // 🔑 生产环境：移除调试代码，符合.cursorrules规范
                    _ = error
                }
            }
        }
    }
}
#endif