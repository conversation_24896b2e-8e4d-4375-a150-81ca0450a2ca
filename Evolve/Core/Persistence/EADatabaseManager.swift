import SwiftData
import Foundation

/// 数据库管理器 - 提供稳定的SwiftData容器创建和错误恢复机制
/// 遵循iOS 17.0+最佳实践，避免过度重置确保数据持久化
@MainActor
class EADatabaseManager: ObservableObject {

    // MARK: - 🔑 架构修复：移除单例模式，改为依赖注入
    // static let shared = EADatabaseManager()  // 已移除单例模式

    // MARK: - 初始化
    init(forTesting: Bool = false) {
        initializeDatabase()
    }
    
    // MARK: - 数据库状态
    enum EADatabaseState {
        case normal          // 正常状态
        case recovered       // 已恢复（修复数据）
        case failed          // 完全失败
    }
    
    @Published var currentState: EADatabaseState = .normal
    @Published var isInitialized: Bool = false
    
    /// 获取当前状态描述
    func getStateDescription() -> String {
        switch currentState {
        case .normal:
            return "正常模式"
        case .recovered:
            return "恢复模式（已修复数据）"
        case .failed:
            return "失败状态"
        }
    }
    
    /// 检查是否需要初始化数据
    func needsInitialization() -> Bool {
        return !UserDefaults.standard.bool(forKey: "EADatabaseInitialized")
    }
    
    /// 标记数据库已初始化
    func markAsInitialized() {
        UserDefaults.standard.set(true, forKey: "EADatabaseInitialized")
    }
    
    /// 强制重置数据库（仅在用户明确要求时使用）
    func forceResetDatabase() -> ModelContainer? {
        // 用户请求强制重置数据库
        
        // 备份现有数据
        backupExistingDatabase()
        
        // 删除现有数据库文件
        do {
            try deleteExistingDatabase()
            // 数据库文件已删除
        } catch {
            // 删除数据库文件失败: \(error)
        }
        
        // 重置初始化标记
        UserDefaults.standard.set(false, forKey: "EADatabaseInitialized")
        
        // 重新创建容器
        return attemptNormalCreation()
    }
    
    // MARK: - 主要方法：创建ModelContainer
    
    /// 创建ModelContainer，使用简化策略确保数据持久化
    /// 策略：正常创建 -> 数据库重置 -> 失败（不使用内存模式）
    func createModelContainer() -> ModelContainer? {
        // 开始创建SwiftData容器
        
        // 第一步：尝试正常创建容器
        if let container = attemptNormalCreation() {
            currentState = .normal
            // 数据库容器创建成功（正常模式）
            return container
        }
        
        // 第二步：如果是Schema问题，尝试数据库重置
        // 正常创建失败，尝试数据库重置
        if let container = attemptDatabaseReset() {
            currentState = .recovered
            // 数据库容器创建成功（重置模式）
            showRecoveryNotification()
            return container
        }
        
        // 第三步：完全失败（不使用内存模式）
        // 数据库创建完全失败
        currentState = .failed
        return nil
    }
    
    // MARK: - 私有方法：创建策略实现
    
    /// 第一步：尝试正常创建容器（iOS 18.2兼容性增强）
    private func attemptNormalCreation() -> ModelContainer? {
        do {
            let schema = createSchema()
            
            // iOS 18.2兼容性：使用更严格的配置
            let configuration = ModelConfiguration(
                isStoredInMemoryOnly: false,
                allowsSave: true,
                groupContainer: .none,
                cloudKitDatabase: .none
            )
            
            let container = try ModelContainer(
                for: schema,
                configurations: configuration
            )
            
            // iOS 18.2兼容性验证：检查容器是否正常工作
            try validateContainer(container)
            
            return container
        } catch {
            // 正常创建失败: \(error.localizedDescription)
            
            // 检查是否为Schema不匹配错误
            if isSchemaError(error) {
                // 检测到Schema不匹配问题，将尝试数据修复
            }
            
            // iOS 18.2特定错误处理
            if isIOS18SchemaError(error) {
                // 检测到iOS 18.2 Schema兼容性问题
            }
            
            logDetailedError(error)
            return nil
        }
    }
    
    /// 尝试数据库重置（仅在Schema错误时使用）
    private func attemptDatabaseReset() -> ModelContainer? {
        do {
            // 备份现有数据库
            backupExistingDatabase()
            
            // 删除现有数据库文件
            try deleteExistingDatabase()
            
            // 重新创建容器
            let schema = createSchema()
            let configuration = ModelConfiguration(
                isStoredInMemoryOnly: false,  // 确保持久化存储
                allowsSave: true
            )
            
            let container = try ModelContainer(
                for: schema,
                configurations: configuration
            )
            
            // 标记需要重新生成初始数据
            UserDefaults.standard.set(false, forKey: "EADatabaseInitialized")
            
            // 数据库重置成功
            return container
            
        } catch {
            // 数据库重置失败: \\(error.localizedDescription)
            logDetailedError(error)
            return nil
        }
    }
    
    // MARK: - 辅助方法
    
    /// 创建数据库Schema（iOS 17.0-18.2+全面兼容版本）
    private func createSchema() -> Schema {
        // 🔧 MVP简化版本：核心模型，移除社区、好友、能量相关模型
        let models: [any PersistentModel.Type] = [
            // 核心模型（有@Relationship）
            EAUser.self,
            EAUserSettings.self,
            EAHabit.self,
            EACompletion.self,

            // 分离的用户档案模型（复杂度控制优化）
            EAUserSocialProfile.self,
            EAUserModerationProfile.self,
            EAUserDataProfile.self,
            EAUserAuthInfo.self,

            // 独立模型（通过外键关联）
            EAAIMessage.self,
            EAPayment.self,
            EAAnalytics.self,
            EAContent.self,
            EAPath.self,

            // 宇宙挑战模型（保留）
            EAUniverseChallenge.self,
            EAUniverseChallengeParticipation.self
        ]
        
        // 创建完整Schema包含模型: \(models.map { String(describing: $0) }.joined(separator: ", "))
        // 优化架构：4个核心模型 + 2个分离档案模型 + 10个独立模型
        return Schema(models)
    }
    
    // MARK: - 持久化验证工具（叶同学专用）
    
    /// 获取数据库统计信息
    func getDatabaseStatistics() async -> (users: Int, habits: Int, completions: Int) {
        do {
            let modelContext = ModelContext(EvolveApp.modelContainer)
            
            let userDescriptor = FetchDescriptor<EAUser>()
            let habitDescriptor = FetchDescriptor<EAHabit>()
            let completionDescriptor = FetchDescriptor<EACompletion>()
            
            let userCount = try modelContext.fetch(userDescriptor).count
            let habitCount = try modelContext.fetch(habitDescriptor).count
            let completionCount = try modelContext.fetch(completionDescriptor).count
            
            return (users: userCount, habits: habitCount, completions: completionCount)
        } catch {
            // 获取数据库统计失败: \(error)
            return (users: 0, habits: 0, completions: 0)
        }
    }
    
    /// 获取数据库文件大小
    func getDatabaseFileSize() -> String {
        let fileManager = FileManager.default
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first else {
            return "未知"
        }
        
        let dbFiles = ["default.store", "Model.sqlite", "DataModel.sqlite"]
        var totalSize: Int64 = 0
        
        for dbFile in dbFiles {
            let dbURL = appSupportURL.appendingPathComponent(dbFile)
            if let attributes = try? fileManager.attributesOfItem(atPath: dbURL.path),
               let fileSize = attributes[.size] as? Int64 {
                totalSize += fileSize
            }
        }
        
        if totalSize == 0 {
            return "暂无数据文件"
        }
        
        // 转换为合适的单位
        if totalSize < 1024 {
            return "\(totalSize) B"
        } else if totalSize < 1024 * 1024 {
            return String(format: "%.1f KB", Double(totalSize) / 1024.0)
        } else {
            return String(format: "%.1f MB", Double(totalSize) / (1024.0 * 1024.0))
        }
    }
    

    
    /// 验证持久化功能（测试保存和读取）
    func validatePersistence() async -> Bool {
        do {
            let modelContext = ModelContext(EvolveApp.modelContainer)
            
            // 创建测试用户
            let testUser = EAUser(username: "持久化测试\(Int.random(in: 1000...9999))", email: "<EMAIL>")
            modelContext.insert(testUser)
            try modelContext.save()
            
            // 立即尝试读取
            let descriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.email == "<EMAIL>" })
            let foundUsers = try modelContext.fetch(descriptor)
            
            // 清理测试数据
            if let userToDelete = foundUsers.first {
                modelContext.delete(userToDelete)
                try modelContext.save()
            }
            
            let success = !foundUsers.isEmpty
            // 持久化验证结果: success
            return success
            
        } catch {
            // 持久化验证出错: \(error)
            return false
        }
    }
    
    /// 删除现有数据库文件
    private func deleteExistingDatabase() throws {
        let fileManager = FileManager.default
        
        // 获取应用支持目录
        guard let applicationSupportURL = fileManager.urls(
            for: .applicationSupportDirectory,
            in: .userDomainMask
        ).first else {
            throw DatabaseError.cannotAccessApplicationSupport
        }
        
        // SwiftData默认数据库文件路径
        let storeURL = applicationSupportURL.appendingPathComponent("default.store")
        
        // 删除主数据库文件
        if fileManager.fileExists(atPath: storeURL.path) {
            try fileManager.removeItem(at: storeURL)
            // 已删除数据库文件: \(storeURL.path)
        }
        
        // 删除相关的辅助文件
        let auxiliaryFiles = [
            "default.store-shm",
            "default.store-wal"
        ]
        
        for fileName in auxiliaryFiles {
            let fileURL = applicationSupportURL.appendingPathComponent(fileName)
            if fileManager.fileExists(atPath: fileURL.path) {
                try? fileManager.removeItem(at: fileURL)
                // 已删除辅助文件: \(fileName)
            }
        }
    }
    
    /// 容器验证（iOS 18.2兼容性增强）
    private func validateContainer(_ container: ModelContainer) throws {
        // 基础验证：尝试获取主上下文
        let mainContext = container.mainContext
        
        // 验证Schema一致性（iOS 18.2增强）
        let schema = mainContext.container.schema
        let _ = [
            "EAUser", "EAUserSettings", "EAHabit", "EACompletion",
            "EAAIMessage", "EAPayment", "EAAnalytics", "EAContent", "EAPath"
        ]
        
        // Schema验证通过，包含所有必要的数据模型
        
        // 简单验证：检查关键模型是否存在
        let hasUserModel = schema.entities.contains { $0.name.contains("EAUser") }
        let hasHabitModel = schema.entities.contains { $0.name.contains("EAHabit") }
        
        if !hasUserModel || !hasHabitModel {
            throw DatabaseError.schemaValidationFailed
        }
        
        // 容器验证通过
    }
    
    /// 检查是否为Schema错误
    private func isSchemaError(_ error: Error) -> Bool {
        let errorDescription = error.localizedDescription.lowercased()
        return errorDescription.contains("no such table") ||
               errorDescription.contains("schema") ||
               errorDescription.contains("column") ||
               errorDescription.contains("couldn't be opened") ||
               errorDescription.contains("database") ||
               errorDescription.contains("sqlite")
    }
    
    /// 检查是否为iOS 18.2 Schema错误
    private func isIOS18SchemaError(_ error: Error) -> Bool {
        let errorDescription = error.localizedDescription.lowercased()
        return errorDescription.contains("column") || 
               errorDescription.contains("schema") ||
               errorDescription.contains("relationship") ||
               errorDescription.contains("associatedhabits")
    }
    
    /// 备份现有数据库（开发环境可选）
    private func backupExistingDatabase() {
        #if DEBUG
        let fileManager = FileManager.default
        
        guard let applicationSupportURL = fileManager.urls(
            for: .applicationSupportDirectory,
            in: .userDomainMask
        ).first else { return }
        
        let storeURL = applicationSupportURL.appendingPathComponent("default.store")
        
        if fileManager.fileExists(atPath: storeURL.path) {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd-HHmmss"
            let timestamp = dateFormatter.string(from: Date())
            
            let backupURL = applicationSupportURL.appendingPathComponent("backup-\(timestamp).store")
            
            try? fileManager.copyItem(at: storeURL, to: backupURL)
            // 数据库已备份到: \(backupURL.path)
        }
        #endif
    }
    
    /// 记录详细错误信息
    private func logDetailedError(_ error: Error) {
        // 详细错误信息:
        //   错误类型: \(type(of: error))
        //   错误描述: \(error.localizedDescription)
        
        // 如果是SwiftData相关错误，记录更多信息
        if let _ = error as? SwiftDataError {
            // 开发阶段可用于调试SwiftData问题
        }
        
        // 记录到系统日志（生产环境可以发送到分析服务）
        #if DEBUG
        //   完整错误: \(error)
        #endif
    }
    
    // MARK: - 用户通知方法
    
    /// 显示数据恢复通知
    private func showRecoveryNotification() {
        // 这里可以显示用户友好的通知
        // 在实际应用中，可以使用Alert或Toast
        // 数据模型已更新，应用已自动清理旧数据并重新初始化
    }
    
    // MARK: - 核心属性
    
    private(set) var modelContainer: ModelContainer?
    private(set) var repositoryContainer: EARepositoryContainerImpl?
    
    // MARK: - 数据库初始化
    
    /// 初始化数据库和Repository层
    func initializeDatabase() {
        // 开始初始化数据库和Repository层
        
        // 创建ModelContainer
        guard let container = createModelContainer() else {
            // 数据库初始化失败
            currentState = .failed
            return
        }
        
        self.modelContainer = container
        
        // 创建Repository容器
        let repoContainer = EARepositoryContainerImpl(modelContainer: container)
        self.repositoryContainer = repoContainer
        
        // ✅ 修复：移除单例调用，Repository注入将在AppEntry中处理
        // EASessionManager.shared.setRepositoryContainer(repoContainer)
        
        // Repository容器创建成功
        
        // 标记初始化完成
        self.isInitialized = true
        // 数据库和Repository层初始化完成
    }
    
    // MARK: - 公共访问方法
    
    /// 获取Repository容器
    func getRepositoryContainer() -> EARepositoryContainer? {
        return repositoryContainer
    }
    
    /// 获取ModelContainer
    func getModelContainer() -> ModelContainer? {
        return modelContainer
    }
    
    /// ✅ 最佳实践：安全的数据库重置（完全重写）
    /// 遵循iOS开发最佳实践，避免文件句柄泄漏
    func safeReinitializeDatabase() {
        #if DEBUG
        print("🔄 [DatabaseManager] 开始安全的数据库重置流程")
        #endif
        
        // 第一步：标记重置状态，避免并发访问
        isInitialized = false
        
        // 第二步：强制释放所有引用，让系统自然清理
        modelContainer = nil
        repositoryContainer = nil
        
        // 第三步：等待系统的自然清理周期（关键：不强制删除文件）
        // iOS会在适当时候自动清理未被引用的ModelContainer文件
        
        // 第四步：清理应用状态，而不是文件系统
        UserDefaults.standard.removeObject(forKey: "EADatabaseInitialized")
        UserDefaults.standard.removeObject(forKey: "hasCreatedDefaultUser")
        UserDefaults.standard.synchronize()
        
        // 第五步：重新初始化（SwiftData会处理文件级别的清理）
        initializeDatabase()
        
        #if DEBUG
        print("✅ [DatabaseManager] 安全数据库重置完成，系统将自动处理文件清理")
        #endif
    }
    
    /// 数据库健康检查
    func performHealthCheck() async -> Bool {
        guard let container = repositoryContainer else {
            // Repository容器不可用
            return false
        }
        
        do {
            // 尝试获取当前用户（基础功能测试）
            _ = try await container.getCurrentUser()
            // 数据库健康检查通过
            return true
        } catch {
            // 数据库健康检查失败: \(error)
            return false
        }
    }
    
    // MARK: - 数据库重置（紧急情况使用）
    
    /// 紧急数据库重置
    func emergencyReset() {
        // 执行紧急数据库重置
        
        // 备份现有数据
        backupExistingDatabase()
        
        // 删除现有数据库文件
        do {
            try deleteExistingDatabase()
            // 数据库文件已删除
        } catch {
            // 删除数据库文件失败: \(error)
        }
        
        // 重置初始化标记
        UserDefaults.standard.set(false, forKey: "EADatabaseInitialized")
        
        // 重新初始化
        safeReinitializeDatabase()
    }
    
    /// 获取数据库文件大小（内部方法版本）
    private func getDatabaseFileSizeInternal() -> String {
        let fileManager = FileManager.default
        
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first else {
            return "未知"
        }
        
        let storeURL = appSupportURL.appendingPathComponent("default.store")
        
        do {
            let attributes = try fileManager.attributesOfItem(atPath: storeURL.path)
            if let fileSize = attributes[.size] as? Int64 {
                return ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file)
            }
        } catch {
            // 无法获取数据库文件大小: \(error)
        }
        
        return "未知"
    }
    
    /// 生成数据库持久化状态报告
    func generatePersistenceReport() async -> String {
        var report = "\n📊 ===== 数据库持久化状态报告 =====\n"
        
        let stats = await getDatabaseStatistics()
        report += "👤 用户数量: \(stats.users)\n"
        report += "🎯 习惯总数: \(stats.habits)\n"
        report += "✅ 完成记录: \(stats.completions)\n"
        
        // 计算数据库文件大小
        let databaseSize = getDatabaseFileSizeInternal()
        report += "💾 数据库大小: \(databaseSize)\n"
        report += "🏠 状态: \(getStateDescription())\n"
        
        // 检查数据库文件存在性
        let fileManager = FileManager.default
        if let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first {
            let storeURL = appSupportURL.appendingPathComponent("default.store")
            let fileExists = fileManager.fileExists(atPath: storeURL.path)
            report += "📁 数据库文件存在: \(fileExists ? "✅ 是" : "❌ 否")\n"
            report += "📂 存储路径: \(storeURL.path)\n"
        }
        
        report += "=====================================\n"
        return report
    }
}

// MARK: - 错误定义

enum DatabaseError: Error, LocalizedError {
    case cannotAccessApplicationSupport
    case containerCreationFailed
    case migrationFailed
    case cannotCreateContainer
    case schemaValidationFailed
    case repositoryCreationFailed
    
    var errorDescription: String? {
        switch self {
        case .cannotAccessApplicationSupport:
            return "无法访问应用支持目录"
        case .containerCreationFailed:
            return "数据库容器创建失败"
        case .migrationFailed:
            return "数据迁移失败"
        case .cannotCreateContainer:
            return "无法创建数据库容器"
        case .schemaValidationFailed:
            return "数据库Schema验证失败"
        case .repositoryCreationFailed:
            return "Repository容器创建失败"
        }
    }
} 